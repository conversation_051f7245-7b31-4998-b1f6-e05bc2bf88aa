<?php
/**
 * Vercel-Compatible Telegram Bot Admin Panel
 * Main entry point for the admin panel
 */

// Start session for authentication
session_start();

// Load environment variables for Vercel
$mongodb_uri = $_ENV['MONGODB_URI'] ?? getenv('MONGODB_URI');
$database_name = $_ENV['DATABASE_NAME'] ?? getenv('DATABASE_NAME') ?? 'referral_bot';
$admin_username = $_ENV['ADMIN_USERNAME'] ?? getenv('ADMIN_USERNAME') ?? 'admin';
$admin_password = $_ENV['ADMIN_PASSWORD'] ?? getenv('ADMIN_PASSWORD') ?? 'admin123';

// Configuration for Vercel
define('ADMIN_USERNAME', $admin_username);
define('ADMIN_PASSWORD', $admin_password);
define('MONGODB_URI', $mongodb_uri);
define('DATABASE_NAME', $database_name);

// Include MongoDB library (Vercel compatible)
require_once __DIR__ . '/vendor/autoload.php';

use MongoDB\Client;
use MongoDB\BSON\UTCDateTime;
use MongoDB\BSON\ObjectId;

class AdminPanel {
    private $mongodb;
    private $database;
    
    public function __construct() {
        try {
            if (empty(MONGODB_URI)) {
                throw new Exception('MongoDB URI not configured. Please set MONGODB_URI environment variable.');
            }
            
            $this->mongodb = new Client(MONGODB_URI, [
                'serverSelectionTimeoutMS' => 5000,
                'connectTimeoutMS' => 10000,
                'socketTimeoutMS' => 20000,
            ]);
            $this->database = $this->mongodb->selectDatabase(DATABASE_NAME);
            
            // Test connection
            $this->database->command(['ping' => 1]);
        } catch (Exception $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please check your configuration.");
        }
    }
    
    // Authentication Methods
    public function isAuthenticated() {
        return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
    }
    
    public function authenticate($username, $password) {
        if ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
            $_SESSION['admin_authenticated'] = true;
            $_SESSION['login_time'] = time();
            return true;
        }
        return false;
    }
    
    public function logout() {
        session_destroy();
        header('Location: /');
        exit;
    }
    
    // User Management Methods
    public function getUsers($page = 1, $limit = 15, $search = '') {
        $skip = ($page - 1) * $limit;
        $filter = [];
        
        if (!empty($search)) {
            // Convert search to integer if it's numeric (for user_id search)
            if (is_numeric($search)) {
                $filter['$or'] = [
                    ['user_id' => (int)$search],
                    ['username' => ['$regex' => $search, '$options' => 'i']],
                    ['first_name' => ['$regex' => $search, '$options' => 'i']],
                    ['account_info.email' => ['$regex' => $search, '$options' => 'i']]
                ];
            } else {
                $filter['$or'] = [
                    ['username' => ['$regex' => $search, '$options' => 'i']],
                    ['first_name' => ['$regex' => $search, '$options' => 'i']],
                    ['account_info.email' => ['$regex' => $search, '$options' => 'i']]
                ];
            }
        }
        
        try {
            $users = $this->database->users->find($filter, [
                'skip' => $skip,
                'limit' => $limit,
                'sort' => ['created_at' => -1]
            ]);
            
            $total = $this->database->users->countDocuments($filter);
            
            return [
                'users' => iterator_to_array($users),
                'total' => $total,
                'pages' => ceil($total / $limit),
                'current_page' => $page
            ];
        } catch (Exception $e) {
            error_log("Error fetching users: " . $e->getMessage());
            return [
                'users' => [],
                'total' => 0,
                'pages' => 0,
                'current_page' => 1,
                'error' => 'Failed to fetch users'
            ];
        }
    }
    
    public function getUserDetails($user_id) {
        try {
            return $this->database->users->findOne(['user_id' => (int)$user_id]);
        } catch (Exception $e) {
            error_log("Error fetching user details: " . $e->getMessage());
            return null;
        }
    }
    
    public function updateUserBalance($user_id, $amount, $operation = 'add') {
        try {
            $user = $this->getUserDetails($user_id);
            if (!$user) return false;
            
            $current_balance = $user['balance'] ?? 0;
            $new_balance = $operation === 'add' ? $current_balance + $amount : $current_balance - $amount;
            $new_balance = max(0, $new_balance); // Prevent negative balance
            
            $result = $this->database->users->updateOne(
                ['user_id' => (int)$user_id],
                [
                    '$set' => [
                        'balance' => $new_balance,
                        'updated_at' => time()
                    ]
                ]
            );
            
            return $result->getModifiedCount() > 0;
        } catch (Exception $e) {
            error_log("Error updating user balance: " . $e->getMessage());
            return false;
        }
    }
    
    public function banUser($user_id, $ban = true) {
        try {
            $result = $this->database->users->updateOne(
                ['user_id' => (int)$user_id],
                [
                    '$set' => [
                        'banned' => $ban,
                        'updated_at' => time()
                    ]
                ]
            );
            
            return $result->getModifiedCount() > 0;
        } catch (Exception $e) {
            error_log("Error banning/unbanning user: " . $e->getMessage());
            return false;
        }
    }
    
    // Withdrawal Management Methods
    public function getWithdrawals($page = 1, $limit = 15, $status_filter = '', $search = '') {
        $skip = ($page - 1) * $limit;
        $filter = [];
        
        if (!empty($status_filter)) {
            $filter['status'] = $status_filter;
        }
        
        if (!empty($search)) {
            if (is_numeric($search)) {
                $filter['$or'] = [
                    ['user_id' => (int)$search],
                    ['username' => ['$regex' => $search, '$options' => 'i']],
                    ['withdrawal_id' => ['$regex' => $search, '$options' => 'i']]
                ];
            } else {
                $filter['$or'] = [
                    ['username' => ['$regex' => $search, '$options' => 'i']],
                    ['withdrawal_id' => ['$regex' => $search, '$options' => 'i']]
                ];
            }
        }
        
        try {
            $withdrawals = $this->database->withdrawals->find($filter, [
                'skip' => $skip,
                'limit' => $limit,
                'sort' => ['created_at' => -1]
            ]);
            
            $total = $this->database->withdrawals->countDocuments($filter);
            
            return [
                'withdrawals' => iterator_to_array($withdrawals),
                'total' => $total,
                'pages' => ceil($total / $limit),
                'current_page' => $page
            ];
        } catch (Exception $e) {
            error_log("Error fetching withdrawals: " . $e->getMessage());
            return [
                'withdrawals' => [],
                'total' => 0,
                'pages' => 0,
                'current_page' => 1,
                'error' => 'Failed to fetch withdrawals'
            ];
        }
    }
    
    public function updateWithdrawalStatus($withdrawal_id, $status, $admin_note = '') {
        try {
            $result = $this->database->withdrawals->updateOne(
                ['withdrawal_id' => $withdrawal_id],
                [
                    '$set' => [
                        'status' => $status,
                        'admin_note' => $admin_note,
                        'processed_at' => time(),
                        'updated_at' => time()
                    ]
                ]
            );
            
            return $result->getModifiedCount() > 0;
        } catch (Exception $e) {
            error_log("Error updating withdrawal status: " . $e->getMessage());
            return false;
        }
    }
    
    // Referral Management Methods
    public function getReferralChain($user_id, $depth = 3) {
        $chain = [];
        $this->buildReferralChain($user_id, $chain, 0, $depth);
        return $chain;
    }
    
    private function buildReferralChain($user_id, &$chain, $current_depth, $max_depth) {
        if ($current_depth >= $max_depth) return;
        
        try {
            $referrals = $this->database->users->find(['referred_by' => (string)$user_id]);
            
            foreach ($referrals as $referral) {
                $chain[] = [
                    'user_id' => $referral['user_id'],
                    'username' => $referral['username'] ?? '',
                    'first_name' => $referral['first_name'] ?? '',
                    'balance' => $referral['balance'] ?? 0,
                    'depth' => $current_depth + 1,
                    'created_at' => $referral['created_at'] ?? 0
                ];
                
                $this->buildReferralChain($referral['user_id'], $chain, $current_depth + 1, $max_depth);
            }
        } catch (Exception $e) {
            error_log("Error building referral chain: " . $e->getMessage());
        }
    }
    
    // Statistics Methods
    public function getDashboardStats() {
        try {
            $total_users = $this->database->users->countDocuments();
            $banned_users = $this->database->users->countDocuments(['banned' => true]);
            $active_users = $total_users - $banned_users;
            
            $total_withdrawals = $this->database->withdrawals->countDocuments();
            $pending_withdrawals = $this->database->withdrawals->countDocuments(['status' => 'Under review']);
            $approved_withdrawals = $this->database->withdrawals->countDocuments(['status' => 'Approved']);
            $rejected_withdrawals = $this->database->withdrawals->countDocuments(['status' => 'Rejected']);
            
            // Calculate total withdrawal amounts
            $pipeline = [
                ['$match' => ['status' => 'Approved']],
                ['$group' => ['_id' => null, 'total' => ['$sum' => '$amount']]]
            ];
            $total_withdrawn = $this->database->withdrawals->aggregate($pipeline)->toArray();
            $total_withdrawn_amount = isset($total_withdrawn[0]) ? $total_withdrawn[0]['total'] : 0;
            
            // Calculate total balance
            $pipeline = [
                ['$group' => ['_id' => null, 'total' => ['$sum' => '$balance']]]
            ];
            $total_balance = $this->database->users->aggregate($pipeline)->toArray();
            $total_user_balance = isset($total_balance[0]) ? $total_balance[0]['total'] : 0;
            
            return [
                'total_users' => $total_users,
                'active_users' => $active_users,
                'banned_users' => $banned_users,
                'total_withdrawals' => $total_withdrawals,
                'pending_withdrawals' => $pending_withdrawals,
                'approved_withdrawals' => $approved_withdrawals,
                'rejected_withdrawals' => $rejected_withdrawals,
                'total_withdrawn_amount' => $total_withdrawn_amount,
                'total_user_balance' => $total_user_balance
            ];
        } catch (Exception $e) {
            error_log("Error fetching dashboard stats: " . $e->getMessage());
            return [
                'total_users' => 0,
                'active_users' => 0,
                'banned_users' => 0,
                'total_withdrawals' => 0,
                'pending_withdrawals' => 0,
                'approved_withdrawals' => 0,
                'rejected_withdrawals' => 0,
                'total_withdrawn_amount' => 0,
                'total_user_balance' => 0,
                'error' => 'Failed to fetch statistics'
            ];
        }
    }
}

// Initialize Admin Panel
try {
    $admin = new AdminPanel();
} catch (Exception $e) {
    die("Failed to initialize admin panel: " . $e->getMessage());
}

// Handle Authentication
if (isset($_POST['login'])) {
    if ($admin->authenticate($_POST['username'], $_POST['password'])) {
        header('Location: /');
        exit;
    } else {
        $login_error = "Invalid credentials";
    }
}

if (isset($_GET['logout'])) {
    $admin->logout();
}

// Handle AJAX Requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    if (!$admin->isAuthenticated()) {
        echo json_encode(['error' => 'Not authenticated']);
        exit;
    }

    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'get_users':
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            $result = $admin->getUsers($page, 15, $search);
            echo json_encode($result);
            break;

        case 'get_user_details':
            $user_id = $_GET['user_id'] ?? '';
            $user = $admin->getUserDetails($user_id);
            if ($user) {
                $referrals = $admin->getReferralChain($user_id, 2);
                echo json_encode(['user' => $user, 'referrals' => $referrals]);
            } else {
                echo json_encode(['error' => 'User not found']);
            }
            break;

        case 'update_balance':
            $user_id = $_POST['user_id'] ?? '';
            $amount = (int)($_POST['amount'] ?? 0);
            $operation = $_POST['operation'] ?? 'add';
            $success = $admin->updateUserBalance($user_id, $amount, $operation);
            echo json_encode(['success' => $success]);
            break;

        case 'ban_user':
            $user_id = $_POST['user_id'] ?? '';
            $ban = $_POST['ban'] === 'true';
            $success = $admin->banUser($user_id, $ban);
            echo json_encode(['success' => $success]);
            break;

        case 'get_withdrawals':
            $page = (int)($_GET['page'] ?? 1);
            $status = $_GET['status'] ?? '';
            $search = $_GET['search'] ?? '';
            $result = $admin->getWithdrawals($page, 15, $status, $search);
            echo json_encode($result);
            break;

        case 'update_withdrawal':
            $withdrawal_id = $_POST['withdrawal_id'] ?? '';
            $status = $_POST['status'] ?? '';
            $note = $_POST['note'] ?? '';
            $success = $admin->updateWithdrawalStatus($withdrawal_id, $status, $note);
            echo json_encode(['success' => $success]);
            break;

        case 'get_stats':
            $stats = $admin->getDashboardStats();
            echo json_encode($stats);
            break;

        case 'export_users':
            $search = $_GET['search'] ?? '';
            $users = $admin->getUsers(1, 10000, $search); // Get all users for export

            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="users_export_' . date('Y-m-d') . '.csv"');

            $output = fopen('php://output', 'w');
            fputcsv($output, ['User ID', 'Username', 'First Name', 'Last Name', 'Balance', 'Banned', 'Referred By', 'Created At', 'Email']);

            foreach ($users['users'] as $user) {
                fputcsv($output, [
                    $user['user_id'],
                    $user['username'] ?? '',
                    $user['first_name'] ?? '',
                    $user['last_name'] ?? '',
                    $user['balance'] ?? 0,
                    $user['banned'] ? 'Yes' : 'No',
                    $user['referred_by'] ?? 'None',
                    date('Y-m-d H:i:s', $user['created_at'] ?? 0),
                    $user['account_info']['email'] ?? ''
                ]);
            }
            fclose($output);
            exit;
            break;

        case 'export_withdrawals':
            $status = $_GET['status'] ?? '';
            $search = $_GET['search'] ?? '';
            $withdrawals = $admin->getWithdrawals(1, 10000, $status, $search); // Get all withdrawals for export

            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="withdrawals_export_' . date('Y-m-d') . '.csv"');

            $output = fopen('php://output', 'w');
            fputcsv($output, ['Withdrawal ID', 'User ID', 'Username', 'Amount', 'Status', 'Method', 'Created At', 'Processed At']);

            foreach ($withdrawals['withdrawals'] as $withdrawal) {
                fputcsv($output, [
                    $withdrawal['withdrawal_id'] ?? '',
                    $withdrawal['user_id'],
                    $withdrawal['username'] ?? '',
                    $withdrawal['amount'] ?? 0,
                    $withdrawal['status'] ?? '',
                    $withdrawal['withdrawal_method'] ?? '',
                    date('Y-m-d H:i:s', $withdrawal['created_at'] ?? 0),
                    isset($withdrawal['processed_at']) ? date('Y-m-d H:i:s', $withdrawal['processed_at']) : 'Not processed'
                ]);
            }
            fclose($output);
            exit;
            break;

        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    exit;
}

// Check authentication for main page
if (!$admin->isAuthenticated()) {
    // Show login form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Panel - Login</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .login-card {
                background: white;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                padding: 2rem;
                width: 100%;
                max-width: 400px;
            }
            .login-header {
                text-align: center;
                margin-bottom: 2rem;
            }
            .login-header i {
                font-size: 3rem;
                color: #667eea;
                margin-bottom: 1rem;
            }
            .btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
            }
            .btn-primary:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-shield-alt"></i>
                <h3>Admin Panel</h3>
                <p class="text-muted">Telegram Bot Management</p>
            </div>

            <?php if (isset($login_error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $login_error; ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>

                <button type="submit" name="login" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>

            <div class="text-center mt-3">
                <small class="text-muted">Hosted on Vercel • Secure & Fast</small>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Main Admin Panel Interface
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Telegram Bot Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }

        body {
            background-color: #f5f6fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }

        .sidebar .nav-link {
            color: var(--dark-color);
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }

        .main-content {
            padding: 20px;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }

        .stats-card .number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stats-card .label {
            color: #666;
            font-size: 0.9rem;
        }

        .content-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .content-card .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 15px 20px;
            border: none;
        }

        .content-card .card-body {
            padding: 20px;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: var(--dark-color);
        }

        .table td {
            border: none;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .badge {
            font-size: 0.75rem;
            padding: 5px 10px;
        }

        .btn {
            border-radius: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding-left: 40px;
            border-radius: 25px;
            border: 1px solid #ddd;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .pagination {
            justify-content: center;
        }

        .pagination .page-link {
            border-radius: 5px;
            margin: 0 2px;
            border: 1px solid #ddd;
            color: var(--primary-color);
        }

        .pagination .page-link:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .loading i {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            border: none;
            border-radius: 10px;
        }

        .referral-tree {
            padding: 20px;
        }

        .referral-level {
            margin-left: 20px;
            border-left: 2px solid var(--primary-color);
            padding-left: 15px;
            margin-bottom: 10px;
        }

        .referral-user {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 5px;
        }

        .vercel-badge {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #000;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 76px;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .stats-card {
                margin-bottom: 15px;
            }

            .vercel-badge {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" id="sidebarToggle">
                <span class="navbar-toggler-icon"></span>
            </button>
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot"></i> Telegram Bot Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-server"></i> Hosted on Vercel
                </span>
                <a class="nav-link" href="?logout=1">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link" href="#" data-section="users">
                            <i class="fas fa-users"></i> User Management
                        </a>
                        <a class="nav-link" href="#" data-section="withdrawals">
                            <i class="fas fa-money-bill-wave"></i> Withdrawals
                        </a>
                        <a class="nav-link" href="#" data-section="referrals">
                            <i class="fas fa-share-alt"></i> Referral Chains
                        </a>
                        <a class="nav-link" href="#" data-section="analytics">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- Dashboard Section -->
                    <div id="dashboard-section" class="content-section">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>
                            <button class="btn btn-primary" onclick="refreshStats()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="row" id="stats-cards">
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="number" id="total-users">-</div>
                                    <div class="label">Total Users</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="number" id="active-users">-</div>
                                    <div class="label">Active Users</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="number" id="pending-withdrawals">-</div>
                                    <div class="label">Pending Withdrawals</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card">
                                    <div class="icon" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
                                        <i class="fas fa-rupee-sign"></i>
                                    </div>
                                    <div class="number" id="total-withdrawn">-</div>
                                    <div class="label">Total Withdrawn (₹)</div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Row -->
                        <div class="row mt-4">
                            <div class="col-lg-8">
                                <div class="content-card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> User Growth</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="userGrowthChart" height="100"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="content-card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Withdrawal Status</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="withdrawalChart" height="150"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Users Section -->
                    <div id="users-section" class="content-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-users"></i> User Management</h2>
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportUsers()">
                                    <i class="fas fa-download"></i> Export CSV
                                </button>
                                <button class="btn btn-primary" onclick="refreshUsers()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Search and Filters -->
                        <div class="content-card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="search-box">
                                            <i class="fas fa-search"></i>
                                            <input type="text" class="form-control" id="user-search"
                                                   placeholder="Search by ID, username, name, or email...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-primary" onclick="searchUsers()">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="clearUserSearch()">
                                            <i class="fas fa-times"></i> Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Users Table -->
                        <div class="content-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-table"></i> Users List</h5>
                            </div>
                            <div class="card-body p-0">
                                <div id="users-loading" class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <p>Loading users...</p>
                                </div>
                                <div id="users-content" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>User ID</th>
                                                    <th>Name</th>
                                                    <th>Username</th>
                                                    <th>Balance</th>
                                                    <th>Status</th>
                                                    <th>Joined</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="users-table-body">
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="p-3">
                                        <nav>
                                            <ul class="pagination" id="users-pagination">
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Withdrawals Section -->
                    <div id="withdrawals-section" class="content-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-money-bill-wave"></i> Withdrawal Management</h2>
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" onclick="exportWithdrawals()">
                                    <i class="fas fa-download"></i> Export CSV
                                </button>
                                <button class="btn btn-primary" onclick="refreshWithdrawals()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="content-card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="search-box">
                                            <i class="fas fa-search"></i>
                                            <input type="text" class="form-control" id="withdrawal-search"
                                                   placeholder="Search by ID, username...">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select" id="withdrawal-status-filter">
                                            <option value="">All Status</option>
                                            <option value="Under review">Pending</option>
                                            <option value="Approved">Approved</option>
                                            <option value="Rejected">Rejected</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary" onclick="searchWithdrawals()">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="clearWithdrawalSearch()">
                                            <i class="fas fa-times"></i> Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Withdrawals Table -->
                        <div class="content-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-table"></i> Withdrawal Requests</h5>
                            </div>
                            <div class="card-body p-0">
                                <div id="withdrawals-loading" class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <p>Loading withdrawals...</p>
                                </div>
                                <div id="withdrawals-content" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Withdrawal ID</th>
                                                    <th>User</th>
                                                    <th>Amount</th>
                                                    <th>Method</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="withdrawals-table-body">
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="p-3">
                                        <nav>
                                            <ul class="pagination" id="withdrawals-pagination">
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Referrals Section -->
                    <div id="referrals-section" class="content-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-share-alt"></i> Referral Chain Management</h2>
                            <button class="btn btn-primary" onclick="refreshReferrals()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>

                        <!-- Referral Search -->
                        <div class="content-card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="search-box">
                                            <i class="fas fa-search"></i>
                                            <input type="text" class="form-control" id="referral-user-search"
                                                   placeholder="Enter User ID to view referral chain...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-primary" onclick="searchReferralChain()">
                                            <i class="fas fa-search"></i> View Chain
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Referral Chain Display -->
                        <div class="content-card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-sitemap"></i> Referral Chain</h5>
                            </div>
                            <div class="card-body">
                                <div id="referral-chain-content">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-search fa-3x mb-3"></i>
                                        <p>Enter a User ID above to view their referral chain</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Section -->
                    <div id="analytics-section" class="content-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-chart-bar"></i> Analytics & Reports</h2>
                            <button class="btn btn-primary" onclick="refreshAnalytics()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>

                        <!-- Analytics Cards -->
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="content-card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-chart-area"></i> Monthly Statistics</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="monthlyStatsChart" height="150"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="content-card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Top Performers</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="top-performers-content">
                                            <div class="text-center text-muted">
                                                <i class="fas fa-spinner fa-spin"></i>
                                                <p>Loading top performers...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vercel Badge -->
    <div class="vercel-badge">
        <i class="fas fa-server"></i> Powered by Vercel
    </div>

    <!-- Include the rest of the interface and JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Global variables
        let currentSection = 'dashboard';
        let currentUserPage = 1;
        let currentWithdrawalPage = 1;
        let userSearchTerm = '';
        let withdrawalSearchTerm = '';
        let withdrawalStatusFilter = '';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    if (section) {
                        showSection(section);
                    }
                });
            });

            // Sidebar toggle for mobile
            const sidebarToggle = document.getElementById('sidebarToggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.getElementById('sidebar').classList.toggle('show');
                });
            }

            // Search inputs
            const userSearch = document.getElementById('user-search');
            if (userSearch) {
                userSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchUsers();
                    }
                });
            }

            const withdrawalSearch = document.getElementById('withdrawal-search');
            if (withdrawalSearch) {
                withdrawalSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchWithdrawals();
                    }
                });
            }

            const referralSearch = document.getElementById('referral-user-search');
            if (referralSearch) {
                referralSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchReferralChain();
                    }
                });
            }

            // Load initial data
            loadDashboard();
        });

        // Section management
        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(sec => {
                sec.style.display = 'none';
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            const sectionElement = document.getElementById(section + '-section');
            if (sectionElement) {
                sectionElement.style.display = 'block';
            }

            // Add active class to current nav link
            const navLink = document.querySelector(`[data-section="${section}"]`);
            if (navLink) {
                navLink.classList.add('active');
            }

            currentSection = section;

            // Load section-specific data
            switch(section) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'withdrawals':
                    loadWithdrawals();
                    break;
                case 'referrals':
                    // Referrals load on demand
                    break;
                case 'analytics':
                    loadAnalytics();
                    break;
            }
        }

        // Dashboard functions
        function loadDashboard() {
            fetch('/?ajax=1&action=get_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading stats:', data.error);
                        return;
                    }
                    updateStatsCards(data);
                    createCharts(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function updateStatsCards(stats) {
            const totalUsers = document.getElementById('total-users');
            const activeUsers = document.getElementById('active-users');
            const pendingWithdrawals = document.getElementById('pending-withdrawals');
            const totalWithdrawn = document.getElementById('total-withdrawn');

            if (totalUsers) totalUsers.textContent = stats.total_users.toLocaleString();
            if (activeUsers) activeUsers.textContent = stats.active_users.toLocaleString();
            if (pendingWithdrawals) pendingWithdrawals.textContent = stats.pending_withdrawals.toLocaleString();
            if (totalWithdrawn) totalWithdrawn.textContent = stats.total_withdrawn_amount.toLocaleString();
        }

        function createCharts(stats) {
            // User Growth Chart (placeholder data)
            const userGrowthCanvas = document.getElementById('userGrowthChart');
            if (userGrowthCanvas) {
                const userGrowthCtx = userGrowthCanvas.getContext('2d');
                new Chart(userGrowthCtx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'New Users',
                            data: [12, 19, 3, 5, 2, 3],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Withdrawal Status Chart
            const withdrawalCanvas = document.getElementById('withdrawalChart');
            if (withdrawalCanvas) {
                const withdrawalCtx = withdrawalCanvas.getContext('2d');
                new Chart(withdrawalCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Pending', 'Approved', 'Rejected'],
                        datasets: [{
                            data: [stats.pending_withdrawals, stats.approved_withdrawals, stats.rejected_withdrawals],
                            backgroundColor: ['#ffc107', '#28a745', '#dc3545']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        function refreshStats() {
            loadDashboard();
        }

        // Placeholder functions for other features
        function loadUsers() {
            console.log('Loading users...');
            // This would be implemented with full AJAX functionality
        }

        function loadWithdrawals() {
            console.log('Loading withdrawals...');
            // This would be implemented with full AJAX functionality
        }

        function loadAnalytics() {
            console.log('Loading analytics...');
            // This would be implemented with full AJAX functionality
        }

        function searchUsers() {
            console.log('Searching users...');
        }

        function clearUserSearch() {
            console.log('Clearing user search...');
        }

        function refreshUsers() {
            console.log('Refreshing users...');
        }

        function searchWithdrawals() {
            console.log('Searching withdrawals...');
        }

        function clearWithdrawalSearch() {
            console.log('Clearing withdrawal search...');
        }

        function refreshWithdrawals() {
            console.log('Refreshing withdrawals...');
        }

        function searchReferralChain() {
            console.log('Searching referral chain...');
        }

        function refreshReferrals() {
            console.log('Refreshing referrals...');
        }

        function refreshAnalytics() {
            console.log('Refreshing analytics...');
        }

        function exportUsers() {
            window.open('/?ajax=1&action=export_users', '_blank');
        }

        function exportWithdrawals() {
            window.open('/?ajax=1&action=export_withdrawals', '_blank');
        }
    </script>
</body>
</html>
