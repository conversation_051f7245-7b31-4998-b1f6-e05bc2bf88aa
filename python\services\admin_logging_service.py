"""
Admin logging service for audit trails
Tracks all admin actions for security and monitoring
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class AdminLoggingService:
    """Service for logging admin actions"""
    
    async def log_admin_action(
        self, 
        admin_id: int, 
        action: str, 
        details: Dict[str, Any] = None,
        target_user_id: int = None
    ) -> bool:
        """Log an admin action"""
        try:
            collection = await get_collection(COLLECTIONS['admin_logs'])
            
            log_entry = {
                "admin_id": admin_id,
                "action": action,
                "details": details or {},
                "target_user_id": target_user_id,
                "timestamp": get_current_timestamp(),
                "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            result = await collection.insert_one(log_entry)
            return result.inserted_id is not None
            
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")
            return False
    
    async def log_admin_added(self, admin_id: int, target_user_id: int) -> bool:
        """Log admin addition"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="admin_added",
            details={"new_admin_id": target_user_id},
            target_user_id=target_user_id
        )
    
    async def log_admin_removed(self, admin_id: int, target_user_id: int) -> bool:
        """Log admin removal"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="admin_removed",
            details={"removed_admin_id": target_user_id},
            target_user_id=target_user_id
        )
    
    async def log_broadcast_started(
        self, 
        admin_id: int, 
        broadcast_id: str, 
        broadcast_type: str,
        total_users: int
    ) -> bool:
        """Log broadcast initiation"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="broadcast_started",
            details={
                "broadcast_id": broadcast_id,
                "broadcast_type": broadcast_type,
                "total_users": total_users
            }
        )
    
    async def log_broadcast_completed(
        self, 
        admin_id: int, 
        broadcast_id: str,
        stats: Dict[str, Any]
    ) -> bool:
        """Log broadcast completion"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="broadcast_completed",
            details={
                "broadcast_id": broadcast_id,
                "statistics": stats
            }
        )
    
    async def log_task_created(self, admin_id: int, task_id: str, task_name: str) -> bool:
        """Log task creation"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="task_created",
            details={
                "task_id": task_id,
                "task_name": task_name
            }
        )
    
    async def log_task_edited(
        self, 
        admin_id: int, 
        task_id: str, 
        field: str, 
        old_value: Any, 
        new_value: Any
    ) -> bool:
        """Log task editing"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="task_edited",
            details={
                "task_id": task_id,
                "field": field,
                "old_value": old_value,
                "new_value": new_value
            }
        )
    
    async def log_task_deleted(self, admin_id: int, task_id: str, task_name: str) -> bool:
        """Log task deletion"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action="task_deleted",
            details={
                "task_id": task_id,
                "task_name": task_name
            }
        )
    
    async def log_user_action(
        self, 
        admin_id: int, 
        action: str, 
        target_user_id: int,
        details: Dict[str, Any] = None
    ) -> bool:
        """Log user management action"""
        return await self.log_admin_action(
            admin_id=admin_id,
            action=f"user_{action}",
            details=details or {},
            target_user_id=target_user_id
        )
    
    async def get_admin_logs(
        self, 
        admin_id: int = None, 
        action: str = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get admin logs with optional filtering"""
        try:
            collection = await get_collection(COLLECTIONS['admin_logs'])
            
            # Build query
            query = {}
            if admin_id:
                query["admin_id"] = admin_id
            if action:
                query["action"] = action
            
            # Get logs sorted by timestamp (newest first)
            cursor = collection.find(query).sort("timestamp", -1).limit(limit)
            logs = await cursor.to_list(length=limit)
            
            return logs
            
        except Exception as e:
            logger.error(f"Error getting admin logs: {e}")
            return []
    
    async def get_admin_statistics(self, admin_id: int = None) -> Dict[str, Any]:
        """Get admin action statistics"""
        try:
            collection = await get_collection(COLLECTIONS['admin_logs'])
            
            # Build base query
            base_query = {}
            if admin_id:
                base_query["admin_id"] = admin_id
            
            # Get total actions
            total_actions = await collection.count_documents(base_query)
            
            # Get actions by type
            pipeline = [
                {"$match": base_query},
                {"$group": {
                    "_id": "$action",
                    "count": {"$sum": 1}
                }},
                {"$sort": {"count": -1}}
            ]
            
            action_counts = await collection.aggregate(pipeline).to_list(length=None)
            
            # Get recent activity (last 24 hours)
            from datetime import datetime, timedelta
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_timestamp = int(yesterday.timestamp())
            
            recent_query = {**base_query, "timestamp": {"$gte": yesterday_timestamp}}
            recent_actions = await collection.count_documents(recent_query)
            
            return {
                "total_actions": total_actions,
                "action_breakdown": {item["_id"]: item["count"] for item in action_counts},
                "recent_actions_24h": recent_actions
            }
            
        except Exception as e:
            logger.error(f"Error getting admin statistics: {e}")
            return {
                "total_actions": 0,
                "action_breakdown": {},
                "recent_actions_24h": 0
            }
