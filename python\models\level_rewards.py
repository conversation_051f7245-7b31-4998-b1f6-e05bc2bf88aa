"""
Level Rewards data models
Maintains identical structure to PHP JSON format
"""

from typing import Dict, List, Any, Optional, Union
from utils.helpers import get_current_timestamp, get_current_date

class LevelRewardsModel:
    """Level rewards data model matching PHP structure exactly"""
    
    @staticmethod
    def get_default_config() -> Dict[str, Any]:
        """Get default level rewards configuration (matching PHP exactly)"""
        return {
            'referral_requirements': [1, 5, 10, 15, 20, 25],
            'bonus_amounts': [2, 10, 15, 20, 25, 30]
        }
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate level rewards configuration"""
        errors = []
        
        if not isinstance(config, dict):
            errors.append("Configuration must be a dictionary")
            return {"valid": False, "errors": errors}
        
        # Check required fields
        if 'referral_requirements' not in config:
            errors.append("Missing referral_requirements")
        
        if 'bonus_amounts' not in config:
            errors.append("Missing bonus_amounts")
        
        if errors:
            return {"valid": False, "errors": errors}
        
        # Validate referral requirements
        referral_reqs = config['referral_requirements']
        if not isinstance(referral_reqs, list) or len(referral_reqs) != 6:
            errors.append("referral_requirements must be a list of 6 integers")
        else:
            for i, req in enumerate(referral_reqs):
                if not isinstance(req, (int, float)) or req < 0:
                    errors.append(f"referral_requirements[{i}] must be a non-negative number")
        
        # Validate bonus amounts
        bonus_amounts = config['bonus_amounts']
        if not isinstance(bonus_amounts, list) or len(bonus_amounts) != 6:
            errors.append("bonus_amounts must be a list of 6 numbers")
        else:
            for i, amount in enumerate(bonus_amounts):
                if not isinstance(amount, (int, float)) or amount < 0:
                    errors.append(f"bonus_amounts[{i}] must be a non-negative number")
        
        return {"valid": len(errors) == 0, "errors": errors}
    
    @staticmethod
    def calculate_user_current_level(referral_count: int, config: Dict[str, Any]) -> int:
        """Calculate user's current level based on referral count (matching PHP logic exactly)"""
        current_level = 0
        
        for level in range(1, 7):  # Levels 1-6
            required_referrals = config['referral_requirements'][level - 1]
            
            if referral_count >= required_referrals:
                current_level = level
            else:
                break
        
        return current_level
    
    @staticmethod
    def get_user_eligible_levels(
        referral_count: int, 
        claimed_levels: List[int], 
        config: Dict[str, Any]
    ) -> List[int]:
        """Get levels user is eligible to claim (matching PHP logic exactly)"""
        eligible_levels = []
        
        for level in range(1, 7):  # Levels 1-6
            required_referrals = config['referral_requirements'][level - 1]
            
            if referral_count >= required_referrals and level not in claimed_levels:
                eligible_levels.append(level)
        
        return eligible_levels
    
    @staticmethod
    def get_next_level_info(
        referral_count: int, 
        current_level: int, 
        config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Get next level information (matching PHP logic exactly)"""
        if current_level >= 6:
            return None  # Max level reached
        
        next_level = current_level + 1
        required_referrals = config['referral_requirements'][next_level - 1]
        remaining_referrals = max(0, required_referrals - referral_count)
        bonus_amount = config['bonus_amounts'][next_level - 1]
        
        return {
            'level': next_level,
            'required_referrals': required_referrals,
            'remaining_referrals': remaining_referrals,
            'bonus_amount': bonus_amount
        }
    
    @staticmethod
    def format_level_rewards_message(
        user_id: int,
        referral_count: int,
        current_level: int,
        claimed_levels: List[int],
        eligible_levels: List[int],
        next_level_info: Optional[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> str:
        """Format level rewards message (matching PHP exactly)"""
        
        message = "🏆 <b>Level Rewards System</b>\n\n"
        message += "👤 <b>Your Status:</b>\n"
        message += f"🔗 <b>Total Referrals:</b> {referral_count}\n"
        message += f"🏅 <b>Current Level:</b> " + (f"Level {current_level}" if current_level > 0 else "No Level") + "\n\n"
        
        message += "📊 <b>All Levels:</b>\n\n"
        
        # Display all 6 levels
        for level in range(1, 7):
            required_referrals = int(config['referral_requirements'][level - 1])
            bonus_amount = float(config['bonus_amounts'][level - 1])
            
            # Determine status
            if level in claimed_levels:
                status = '✅ Claimed'
            elif level in eligible_levels:
                status = '🎁 Ready to Claim'
            elif referral_count >= required_referrals:
                status = '🔓 Unlocked'
            else:
                status = '🔒 Locked'
            
            message += f"<b>Level {level}:</b> {required_referrals} referrals = ₹{bonus_amount} | {status}\n"
        
        # Next level progress
        if next_level_info:
            message += f"\n🎯 <b>Next Level Progress:</b>\n"
            message += f"Level {next_level_info['level']}: Need {next_level_info['remaining_referrals']} more referrals for ₹{next_level_info['bonus_amount']} bonus\n"
        else:
            message += f"\n🎉 <b>Congratulations! You've reached the maximum level!</b>\n"
        
        # Unclaimed bonuses notification
        if eligible_levels:
            message += f"\n💰 <b>You have unclaimed level bonuses available!</b>"
        
        # Real-time sync indicator (matching PHP)
        from datetime import datetime
        current_time = datetime.now().strftime('%H:%M:%S')
        message += f"\n\n<i>🔄 Config synced: {current_time}</i>"
        
        return message
    
    @staticmethod
    def format_level_claim_success_message(
        level: int, 
        amount: float, 
        user_balance: float
    ) -> str:
        """Format level claim success message (matching PHP exactly)"""
        message = "🎉 <b>Level Bonus Claimed Successfully!</b>\n\n"
        message += f"🏅 <b>Level:</b> {level}\n"
        message += f"💰 <b>Bonus Amount:</b> ₹{amount}\n\n"
        message += "✅ The bonus has been added to your wallet!"
        
        return message
    
    @staticmethod
    def format_follow_up_message(next_level: int, next_bonus: float) -> str:
        """Format follow-up message for next available level (matching PHP exactly)"""
        message = "🎁 <b>Great news!</b> You have another level bonus ready to claim!\n\n"
        message += f"Level {next_level} bonus: ₹{next_bonus}"
        
        return message
    
    @staticmethod
    def format_admin_config_message(config: Dict[str, Any], enabled: bool) -> str:
        """Format admin configuration message (matching PHP exactly)"""
        message = "🏆 <b>Level Rewards Configuration</b>\n\n"
        message += f"⚙️ <b>Status:</b> " + ("✅ Enabled" if enabled else "❌ Disabled") + "\n\n"
        message += "📊 <b>Current Configuration:</b>\n\n"
        
        for level in range(1, 7):
            referrals = config['referral_requirements'][level - 1]
            bonus = config['bonus_amounts'][level - 1]
            message += f"<b>Level {level}:</b> {referrals} referrals = ₹{bonus}\n"
        
        return message
    
    @staticmethod
    def format_config_update_success_message(
        referrals: List[int], 
        bonuses: List[float]
    ) -> str:
        """Format configuration update success message (matching PHP exactly)"""
        message = "✅ <b>Level Rewards Configuration Updated!</b>\n\n"
        message += "📊 <b>New Configuration:</b>\n\n"
        
        for level in range(1, 7):
            referral_req = referrals[level - 1]
            bonus_amount = bonuses[level - 1]
            message += f"<b>Level {level}:</b> {referral_req} referrals = ₹{bonus_amount}\n"
        
        message += "\n🎉 The new configuration is now active!"
        
        return message
    
    @staticmethod
    def format_toggle_success_message(enabled: bool, config: Dict[str, Any]) -> str:
        """Format toggle success message (matching PHP exactly)"""
        status_text = "✅ Enabled" if enabled else "❌ Disabled"
        action_text = "enabled" if enabled else "disabled"
        
        message = "🔄 <b>Level Rewards System Updated!</b>\n\n"
        message += f"⚙️ <b>Status:</b> {status_text}\n\n"
        message += f"The level rewards system has been {action_text}."
        
        if enabled:
            message += "\n\n📊 <b>Current Configuration:</b>\n"
            for level in range(1, 7):
                referrals = config['referral_requirements'][level - 1]
                bonus = config['bonus_amounts'][level - 1]
                message += f"Level {level}: {referrals} referrals = ₹{bonus}\n"
        
        return message
    
    @staticmethod
    def create_level_rewards_keyboard(eligible_levels: List[int], config: Dict[str, Any]):
        """Create level rewards keyboard (matching PHP exactly)"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard_buttons = []
        
        if eligible_levels:
            # Show claim button for the lowest eligible level
            lowest_level = min(eligible_levels)
            bonus_amount = float(config['bonus_amounts'][lowest_level - 1])
            
            keyboard_buttons.append([
                InlineKeyboardButton(
                    f"💰 Claim Level {lowest_level} Bonus (₹{bonus_amount})", 
                    callback_data=f"claimLevel_{lowest_level}"
                )
            ])
        
        keyboard_buttons.append([
            InlineKeyboardButton('↩️ Back to Extra Rewards', callback_data='extraRewards')
        ])
        
        return InlineKeyboardMarkup(keyboard_buttons)
    
    @staticmethod
    def create_admin_config_keyboard(enabled: bool):
        """Create admin configuration keyboard (matching PHP exactly)"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        toggle_text = "❌ Disable" if enabled else "✅ Enable"
        
        keyboard_buttons = [
            [InlineKeyboardButton('⚙️ Configure Levels', callback_data='configureLevelSettings')],
            [InlineKeyboardButton(f'{toggle_text} Level Rewards', callback_data='toggleLevelBonus')],
            [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
        ]
        
        return InlineKeyboardMarkup(keyboard_buttons)
    
    @staticmethod
    def create_claim_success_keyboard():
        """Create claim success keyboard (matching PHP exactly)"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard_buttons = [
            [InlineKeyboardButton('🏆 View Level Rewards', callback_data='levelRewards')],
            [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
        ]
        
        return InlineKeyboardMarkup(keyboard_buttons)
