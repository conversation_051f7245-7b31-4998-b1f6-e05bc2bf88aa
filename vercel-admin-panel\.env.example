# Telegram Bot Admin Panel Environment Variables
# Copy this file to .env and fill in your actual values
# DO NOT commit .env to version control

# MongoDB Configuration
MONGODB_URI=mongodb+srv://tashanwinofficial:<EMAIL>/testingreferralbot?retryWrites=true&w=majority
DATABASE_NAME=referral_bot

# Admin Authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=1412

# Optional: Additional Security
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5

# Optional: Feature Flags
ENABLE_EXPORT=true
ENABLE_ANALYTICS=true

# Optional: Timezone
TIMEZONE=Asia/Kolkata
