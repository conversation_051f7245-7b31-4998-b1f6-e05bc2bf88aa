"""
Task data models
Maintains identical structure to PHP JSON format
"""

from typing import Dict, List, Any, Optional
from utils.helpers import get_current_timestamp, generate_task_id, generate_submission_id

class TaskModel:
    """Task data model matching PHP JSON structure exactly"""
    
    @staticmethod
    def create_new_task(
        name: str,
        description: str,
        reward_amount: int,
        media_url: str = "",
        status: str = "active",
        created_by: int = 0
    ) -> Dict[str, Any]:
        """Create new task document (matching PHP structure)"""
        
        return {
            "name": name,
            "description": description,
            "reward_amount": reward_amount,
            "media_url": media_url,
            "status": status,
            "created_by": created_by,
            "task_id": generate_task_id(),
            "created_at": get_current_timestamp()
        }
    
    @staticmethod
    def validate_task_data(task_data: Dict[str, Any]) -> bool:
        """Validate task data structure"""
        required_fields = [
            'name', 'description', 'reward_amount', 'status', 
            'created_by', 'task_id', 'created_at'
        ]
        
        for field in required_fields:
            if field not in task_data:
                return False
        
        # Validate field types and constraints
        if not isinstance(task_data['name'], str) or len(task_data['name']) > 50:
            return False
        
        if not isinstance(task_data['description'], str) or len(task_data['description']) < 10:
            return False
        
        if not isinstance(task_data['reward_amount'], (int, float)) or task_data['reward_amount'] <= 0:
            return False
        
        if task_data['status'] not in ['active', 'inactive']:
            return False
        
        return True
    
    @staticmethod
    def update_task_status(task_data: Dict[str, Any], status: str) -> Dict[str, Any]:
        """Update task status"""
        task_data['status'] = status
        task_data['updated_at'] = get_current_timestamp()
        return task_data
    
    @staticmethod
    def update_task_data(task_data: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update task data"""
        task_data.update(updates)
        task_data['updated_at'] = get_current_timestamp()
        return task_data

class TaskSubmissionModel:
    """Task submission data model matching PHP JSON structure exactly"""
    
    @staticmethod
    def create_new_submission(
        user_id: int,
        task_id: str,
        file_id: str,
        status: str = "pending"
    ) -> Dict[str, Any]:
        """Create new task submission document (matching PHP structure)"""
        
        return {
            "user_id": user_id,
            "task_id": task_id,
            "file_id": file_id,
            "status": status,
            "submission_id": generate_submission_id(),
            "submitted_at": get_current_timestamp(),
            "admin_note": "",
            "reviewed_at": None
        }
    
    @staticmethod
    def validate_submission_data(submission_data: Dict[str, Any]) -> bool:
        """Validate submission data structure"""
        required_fields = [
            'user_id', 'task_id', 'file_id', 'status',
            'submission_id', 'submitted_at'
        ]
        
        for field in required_fields:
            if field not in submission_data:
                return False
        
        # Validate status
        if submission_data['status'] not in ['pending', 'approved', 'rejected']:
            return False
        
        return True
    
    @staticmethod
    def approve_submission(submission_data: Dict[str, Any], admin_note: str = "") -> Dict[str, Any]:
        """Approve task submission"""
        submission_data['status'] = 'approved'
        submission_data['admin_note'] = admin_note
        submission_data['reviewed_at'] = get_current_timestamp()
        return submission_data
    
    @staticmethod
    def reject_submission(submission_data: Dict[str, Any], admin_note: str = "") -> Dict[str, Any]:
        """Reject task submission"""
        submission_data['status'] = 'rejected'
        submission_data['admin_note'] = admin_note
        submission_data['reviewed_at'] = get_current_timestamp()
        return submission_data
    
    @staticmethod
    def get_submission_status_display(status: str) -> str:
        """Get display text for submission status"""
        status_map = {
            'pending': '⏳ Pending Review',
            'approved': '✅ Approved',
            'rejected': '❌ Rejected'
        }
        return status_map.get(status, '❓ Unknown')
    
    @staticmethod
    def get_submission_status_emoji(status: str) -> str:
        """Get emoji for submission status"""
        status_map = {
            'pending': '⏳',
            'approved': '✅',
            'rejected': '❌'
        }
        return status_map.get(status, '❓')

class TaskStatsModel:
    """Task statistics model"""
    
    @staticmethod
    def calculate_task_stats(tasks: List[Dict[str, Any]], submissions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate task statistics"""
        total_tasks = len(tasks)
        active_tasks = len([t for t in tasks if t.get('status') == 'active'])
        inactive_tasks = len([t for t in tasks if t.get('status') == 'inactive'])
        
        total_submissions = len(submissions)
        pending_submissions = len([s for s in submissions if s.get('status') == 'pending'])
        approved_submissions = len([s for s in submissions if s.get('status') == 'approved'])
        rejected_submissions = len([s for s in submissions if s.get('status') == 'rejected'])
        
        total_rewards_distributed = sum(
            task.get('reward_amount', 0) 
            for task in tasks 
            for submission in submissions 
            if submission.get('task_id') == task.get('task_id') and submission.get('status') == 'approved'
        )
        
        return {
            'total_tasks': total_tasks,
            'active_tasks': active_tasks,
            'inactive_tasks': inactive_tasks,
            'total_submissions': total_submissions,
            'pending_submissions': pending_submissions,
            'approved_submissions': approved_submissions,
            'rejected_submissions': rejected_submissions,
            'total_rewards_distributed': total_rewards_distributed
        }
    
    @staticmethod
    def get_task_submission_count(task_id: str, submissions: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get submission count for a specific task"""
        task_submissions = [s for s in submissions if s.get('task_id') == task_id]
        
        return {
            'total': len(task_submissions),
            'pending': len([s for s in task_submissions if s.get('status') == 'pending']),
            'approved': len([s for s in task_submissions if s.get('status') == 'approved']),
            'rejected': len([s for s in task_submissions if s.get('status') == 'rejected'])
        }
    
    @staticmethod
    def get_user_task_history(user_id: int, submissions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Get user's task submission history"""
        user_submissions = [s for s in submissions if s.get('user_id') == user_id]
        
        # Sort by submission date (newest first)
        user_submissions.sort(key=lambda x: x.get('submitted_at', 0), reverse=True)
        
        return user_submissions
    
    @staticmethod
    def check_user_task_submission(user_id: int, task_id: str, submissions: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Check if user has submitted a specific task"""
        for submission in submissions:
            if submission.get('user_id') == user_id and submission.get('task_id') == task_id:
                return submission
        return None
    
    @staticmethod
    def get_pending_submissions_for_admin(submissions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Get pending submissions for admin review"""
        pending = [s for s in submissions if s.get('status') == 'pending']
        
        # Sort by submission date (oldest first for admin review)
        pending.sort(key=lambda x: x.get('submitted_at', 0))
        
        return pending
