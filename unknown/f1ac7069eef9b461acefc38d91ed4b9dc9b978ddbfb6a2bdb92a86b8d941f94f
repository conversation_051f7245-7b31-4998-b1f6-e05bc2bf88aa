"""
Analytics service for comprehensive bot statistics and reporting
Maintains identical functionality to PHP version
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp, get_current_date

logger = logging.getLogger(__name__)

class AnalyticsService:
    """Service for analytics and reporting operations"""
    
    def __init__(self):
        pass
    
    async def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """Get comprehensive bot statistics (matching PHP logic exactly)"""
        try:
            stats = {}
            
            # User statistics
            user_stats = await self.get_user_statistics()
            stats.update(user_stats)
            
            # Financial statistics
            financial_stats = await self.get_financial_statistics()
            stats.update(financial_stats)
            
            # Task statistics
            task_stats = await self.get_task_statistics()
            stats.update(task_stats)
            
            # Referral statistics
            referral_stats = await self.get_referral_statistics()
            stats.update(referral_stats)
            
            # System statistics
            system_stats = await self.get_system_statistics()
            stats.update(system_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting comprehensive statistics: {e}")
            return {}

    async def get_total_users(self) -> int:
        """Get total number of users"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            return await users_collection.count_documents({})
        except Exception as e:
            logger.error(f"Error getting total users: {e}")
            return 0

    async def get_active_users_today(self) -> int:
        """Get number of active users today"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            today = get_current_date()
            return await users_collection.count_documents({
                "last_activity": {"$regex": f"^{today}"}
            })
        except Exception as e:
            logger.error(f"Error getting active users today: {e}")
            return 0

    async def get_new_users_today(self) -> int:
        """Get number of new users today"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            today = get_current_date()
            return await users_collection.count_documents({
                "created_at": {"$regex": f"^{today}"}
            })
        except Exception as e:
            logger.error(f"Error getting new users today: {e}")
            return 0

    async def get_total_referrals(self) -> int:
        """Get total number of referrals"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            pipeline = [
                {"$unwind": "$referrals"},
                {"$count": "total_referrals"}
            ]
            result = await users_collection.aggregate(pipeline).to_list(1)
            return result[0]["total_referrals"] if result else 0
        except Exception as e:
            logger.error(f"Error getting total referrals: {e}")
            return 0

    async def get_total_withdrawals(self) -> int:
        """Get total number of withdrawals"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            pipeline = [
                {"$unwind": "$withdrawal_reports"},
                {"$count": "total_withdrawals"}
            ]
            result = await users_collection.aggregate(pipeline).to_list(1)
            return result[0]["total_withdrawals"] if result else 0
        except Exception as e:
            logger.error(f"Error getting total withdrawals: {e}")
            return 0

    async def get_pending_withdrawals_count(self) -> int:
        """Get number of pending withdrawals"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            pipeline = [
                {"$unwind": "$withdrawal_reports"},
                {"$match": {"withdrawal_reports.status": "Under review"}},
                {"$count": "pending_withdrawals"}
            ]
            result = await users_collection.aggregate(pipeline).to_list(1)
            return result[0]["pending_withdrawals"] if result else 0
        except Exception as e:
            logger.error(f"Error getting pending withdrawals count: {e}")
            return 0

    async def get_total_balance_distributed(self) -> float:
        """Get total balance distributed to users"""
        try:
            users_collection = await get_collection(COLLECTIONS['USERS'])
            pipeline = [
                {"$group": {"_id": None, "total_balance": {"$sum": "$balance"}}}
            ]
            result = await users_collection.aggregate(pipeline).to_list(1)
            return result[0]["total_balance"] if result else 0
        except Exception as e:
            logger.error(f"Error getting total balance distributed: {e}")
            return 0
    
    async def get_user_statistics(self) -> Dict[str, Any]:
        """Get user-related statistics (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            # Total users
            total_users = await collection.count_documents({})
            
            # Active users (not banned)
            active_users = await collection.count_documents({"banned": {"$ne": True}})
            
            # Banned users
            banned_users = await collection.count_documents({"banned": True})
            
            # Users joined today
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_timestamp = int(today_start.timestamp())
            
            users_today = await collection.count_documents({
                "created_at": {"$gte": today_timestamp}
            })
            
            # Users joined this week
            week_start = today_start - timedelta(days=7)
            week_timestamp = int(week_start.timestamp())
            
            users_this_week = await collection.count_documents({
                "created_at": {"$gte": week_timestamp}
            })
            
            # Users joined this month
            month_start = today_start.replace(day=1)
            month_timestamp = int(month_start.timestamp())
            
            users_this_month = await collection.count_documents({
                "created_at": {"$gte": month_timestamp}
            })
            
            # Users with balance > 0
            users_with_balance = await collection.count_documents({
                "balance": {"$gt": 0}
            })
            
            # Top users by balance
            top_users_cursor = collection.find({}).sort("balance", -1).limit(5)
            top_users = await top_users_cursor.to_list(length=5)
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'banned_users': banned_users,
                'users_today': users_today,
                'users_this_week': users_this_week,
                'users_this_month': users_this_month,
                'users_with_balance': users_with_balance,
                'top_users': top_users
            }
            
        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            return {}
    
    async def get_financial_statistics(self) -> Dict[str, Any]:
        """Get financial statistics (matching PHP logic exactly)"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])
            
            # Total balance in system
            pipeline = [
                {"$group": {"_id": None, "total_balance": {"$sum": "$balance"}}}
            ]
            balance_result = await users_collection.aggregate(pipeline).to_list(length=1)
            total_balance = balance_result[0]['total_balance'] if balance_result else 0
            
            # Withdrawal statistics
            total_withdrawals = await withdrawals_collection.count_documents({})
            
            pending_withdrawals = await withdrawals_collection.count_documents({
                "status": "Pending"
            })
            
            approved_withdrawals = await withdrawals_collection.count_documents({
                "status": "Passed"
            })
            
            rejected_withdrawals = await withdrawals_collection.count_documents({
                "status": "Failed"
            })
            
            # Total withdrawal amounts
            pipeline = [
                {"$match": {"status": "Passed"}},
                {"$group": {"_id": None, "total_amount": {"$sum": "$amount"}}}
            ]
            withdrawal_amount_result = await withdrawals_collection.aggregate(pipeline).to_list(length=1)
            total_withdrawn = withdrawal_amount_result[0]['total_amount'] if withdrawal_amount_result else 0
            
            # Pending withdrawal amount
            pipeline = [
                {"$match": {"status": "Pending"}},
                {"$group": {"_id": None, "total_amount": {"$sum": "$amount"}}}
            ]
            pending_amount_result = await withdrawals_collection.aggregate(pipeline).to_list(length=1)
            pending_amount = pending_amount_result[0]['total_amount'] if pending_amount_result else 0
            
            # Today's withdrawals
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_timestamp = int(today_start.timestamp())
            
            withdrawals_today = await withdrawals_collection.count_documents({
                "created_at": {"$gte": today_timestamp}
            })
            
            return {
                'total_balance': total_balance,
                'total_withdrawals': total_withdrawals,
                'pending_withdrawals': pending_withdrawals,
                'approved_withdrawals': approved_withdrawals,
                'rejected_withdrawals': rejected_withdrawals,
                'total_withdrawn': total_withdrawn,
                'pending_amount': pending_amount,
                'withdrawals_today': withdrawals_today
            }
            
        except Exception as e:
            logger.error(f"Error getting financial statistics: {e}")
            return {}
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """Get task-related statistics (matching PHP logic exactly)"""
        try:
            tasks_collection = await get_collection(COLLECTIONS['tasks'])
            submissions_collection = await get_collection(COLLECTIONS['task_submissions'])
            
            # Total tasks
            total_tasks = await tasks_collection.count_documents({})
            
            # Active tasks
            active_tasks = await tasks_collection.count_documents({
                "active": True
            })
            
            # Total submissions
            total_submissions = await submissions_collection.count_documents({})
            
            # Pending submissions
            pending_submissions = await submissions_collection.count_documents({
                "status": "Pending"
            })
            
            # Approved submissions
            approved_submissions = await submissions_collection.count_documents({
                "status": "Approved"
            })
            
            # Rejected submissions
            rejected_submissions = await submissions_collection.count_documents({
                "status": "Rejected"
            })
            
            # Today's submissions
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_timestamp = int(today_start.timestamp())
            
            submissions_today = await submissions_collection.count_documents({
                "created_at": {"$gte": today_timestamp}
            })
            
            return {
                'total_tasks': total_tasks,
                'active_tasks': active_tasks,
                'total_submissions': total_submissions,
                'pending_submissions': pending_submissions,
                'approved_submissions': approved_submissions,
                'rejected_submissions': rejected_submissions,
                'submissions_today': submissions_today
            }
            
        except Exception as e:
            logger.error(f"Error getting task statistics: {e}")
            return {}
    
    async def get_referral_statistics(self) -> Dict[str, Any]:
        """Get referral-related statistics (matching PHP logic exactly)"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            custom_referrals_collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            # Total referrals
            pipeline = [
                {"$unwind": "$promotion_report"},
                {"$count": "total_referrals"}
            ]
            referral_result = await users_collection.aggregate(pipeline).to_list(length=1)
            total_referrals = referral_result[0]['total_referrals'] if referral_result else 0
            
            # Users with referrals
            users_with_referrals = await users_collection.count_documents({
                "promotion_report": {"$exists": True, "$ne": []}
            })
            
            # Custom referral links
            total_custom_referrals = await custom_referrals_collection.count_documents({})
            
            # Active custom referrals
            active_custom_referrals = await custom_referrals_collection.count_documents({
                "active": True
            })
            
            # Total custom referral clicks
            pipeline = [
                {"$group": {"_id": None, "total_clicks": {"$sum": "$clicks"}}}
            ]
            clicks_result = await custom_referrals_collection.aggregate(pipeline).to_list(length=1)
            total_custom_clicks = clicks_result[0]['total_clicks'] if clicks_result else 0
            
            # Total custom referral conversions
            pipeline = [
                {"$group": {"_id": None, "total_conversions": {"$sum": "$referrals"}}}
            ]
            conversions_result = await custom_referrals_collection.aggregate(pipeline).to_list(length=1)
            total_custom_conversions = conversions_result[0]['total_conversions'] if conversions_result else 0
            
            return {
                'total_referrals': total_referrals,
                'users_with_referrals': users_with_referrals,
                'total_custom_referrals': total_custom_referrals,
                'active_custom_referrals': active_custom_referrals,
                'total_custom_clicks': total_custom_clicks,
                'total_custom_conversions': total_custom_conversions
            }
            
        except Exception as e:
            logger.error(f"Error getting referral statistics: {e}")
            return {}
    
    async def get_system_statistics(self) -> Dict[str, Any]:
        """Get system-related statistics (matching PHP logic exactly)"""
        try:
            # Force subscription channels
            force_channels_collection = await get_collection(COLLECTIONS['force_channels'])
            total_force_channels = await force_channels_collection.count_documents({})
            
            # Gift codes
            gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])
            total_gift_codes = await gift_codes_collection.count_documents({})
            active_gift_codes = await gift_codes_collection.count_documents({
                "$and": [
                    {"$expr": {"$lt": ["$used_count", "$usage_limit"]}},
                    {"$or": [{"expiry_date": 0}, {"expiry_date": {"$gt": get_current_timestamp()}}]}
                ]
            })
            
            # Admin logs
            admin_logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            total_admin_actions = await admin_logs_collection.count_documents({})
            
            # Today's admin actions
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_timestamp = int(today_start.timestamp())
            
            admin_actions_today = await admin_logs_collection.count_documents({
                "timestamp": {"$gte": today_timestamp}
            })
            
            return {
                'total_force_channels': total_force_channels,
                'total_gift_codes': total_gift_codes,
                'active_gift_codes': active_gift_codes,
                'total_admin_actions': total_admin_actions,
                'admin_actions_today': admin_actions_today,
                'last_updated': get_current_timestamp()
            }
            
        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
            return {}

    async def get_daily_statistics(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get daily statistics for the last N days (matching PHP logic exactly)"""
        try:
            daily_stats = []

            for i in range(days):
                # Calculate date range for this day
                target_date = datetime.now() - timedelta(days=i)
                day_start = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
                day_end = day_start + timedelta(days=1)

                start_timestamp = int(day_start.timestamp())
                end_timestamp = int(day_end.timestamp())

                # Get statistics for this day
                day_stats = await self._get_day_statistics(start_timestamp, end_timestamp)
                day_stats['date'] = day_start.strftime('%Y-%m-%d')
                day_stats['day_name'] = day_start.strftime('%A')

                daily_stats.append(day_stats)

            return daily_stats

        except Exception as e:
            logger.error(f"Error getting daily statistics: {e}")
            return []

    async def _get_day_statistics(self, start_timestamp: int, end_timestamp: int) -> Dict[str, Any]:
        """Get statistics for a specific day"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])
            submissions_collection = await get_collection(COLLECTIONS['task_submissions'])

            # New users
            new_users = await users_collection.count_documents({
                "created_at": {"$gte": start_timestamp, "$lt": end_timestamp}
            })

            # New withdrawals
            new_withdrawals = await withdrawals_collection.count_documents({
                "created_at": {"$gte": start_timestamp, "$lt": end_timestamp}
            })

            # Withdrawal amount for the day
            pipeline = [
                {"$match": {
                    "created_at": {"$gte": start_timestamp, "$lt": end_timestamp},
                    "status": "Passed"
                }},
                {"$group": {"_id": None, "total_amount": {"$sum": "$amount"}}}
            ]
            withdrawal_result = await withdrawals_collection.aggregate(pipeline).to_list(length=1)
            withdrawal_amount = withdrawal_result[0]['total_amount'] if withdrawal_result else 0

            # New task submissions
            new_submissions = await submissions_collection.count_documents({
                "created_at": {"$gte": start_timestamp, "$lt": end_timestamp}
            })

            return {
                'new_users': new_users,
                'new_withdrawals': new_withdrawals,
                'withdrawal_amount': withdrawal_amount,
                'new_submissions': new_submissions
            }

        except Exception as e:
            logger.error(f"Error getting day statistics: {e}")
            return {}

    async def get_top_performers(self) -> Dict[str, Any]:
        """Get top performing users and statistics (matching PHP logic exactly)"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            # Top users by balance
            top_balance_cursor = users_collection.find({}).sort("balance", -1).limit(10)
            top_balance = await top_balance_cursor.to_list(length=10)

            # Top users by referrals
            pipeline = [
                {"$addFields": {"referral_count": {"$size": {"$ifNull": ["$promotion_report", []]}}}},
                {"$sort": {"referral_count": -1}},
                {"$limit": 10}
            ]
            top_referrals = await users_collection.aggregate(pipeline).to_list(length=10)

            # Top users by task submissions
            submissions_collection = await get_collection(COLLECTIONS['task_submissions'])
            pipeline = [
                {"$group": {"_id": "$user_id", "submission_count": {"$sum": 1}}},
                {"$sort": {"submission_count": -1}},
                {"$limit": 10}
            ]
            submission_counts = await submissions_collection.aggregate(pipeline).to_list(length=10)

            # Get user details for top submitters
            top_submitters = []
            for item in submission_counts:
                user = await users_collection.find_one({"user_id": item['_id']})
                if user:
                    user['submission_count'] = item['submission_count']
                    top_submitters.append(user)

            return {
                'top_balance': top_balance,
                'top_referrals': top_referrals,
                'top_submitters': top_submitters
            }

        except Exception as e:
            logger.error(f"Error getting top performers: {e}")
            return {}

    async def get_growth_metrics(self) -> Dict[str, Any]:
        """Get growth metrics and trends (matching PHP logic exactly)"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            # Calculate growth rates
            now = datetime.now()

            # This month vs last month
            this_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)

            this_month_timestamp = int(this_month_start.timestamp())
            last_month_timestamp = int(last_month_start.timestamp())

            users_this_month = await users_collection.count_documents({
                "created_at": {"$gte": this_month_timestamp}
            })

            users_last_month = await users_collection.count_documents({
                "created_at": {"$gte": last_month_timestamp, "$lt": this_month_timestamp}
            })

            # Calculate growth rate
            if users_last_month > 0:
                monthly_growth_rate = ((users_this_month - users_last_month) / users_last_month) * 100
            else:
                monthly_growth_rate = 100 if users_this_month > 0 else 0

            # This week vs last week
            this_week_start = now - timedelta(days=now.weekday())
            this_week_start = this_week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            last_week_start = this_week_start - timedelta(days=7)

            this_week_timestamp = int(this_week_start.timestamp())
            last_week_timestamp = int(last_week_start.timestamp())

            users_this_week = await users_collection.count_documents({
                "created_at": {"$gte": this_week_timestamp}
            })

            users_last_week = await users_collection.count_documents({
                "created_at": {"$gte": last_week_timestamp, "$lt": this_week_timestamp}
            })

            # Calculate weekly growth rate
            if users_last_week > 0:
                weekly_growth_rate = ((users_this_week - users_last_week) / users_last_week) * 100
            else:
                weekly_growth_rate = 100 if users_this_week > 0 else 0

            return {
                'users_this_month': users_this_month,
                'users_last_month': users_last_month,
                'monthly_growth_rate': round(monthly_growth_rate, 2),
                'users_this_week': users_this_week,
                'users_last_week': users_last_week,
                'weekly_growth_rate': round(weekly_growth_rate, 2)
            }

        except Exception as e:
            logger.error(f"Error getting growth metrics: {e}")
            return {}

    async def get_top_users_by_withdrawals(self, limit: int = 15) -> List[Dict[str, Any]]:
        """Get top users by withdrawal amount (matching PHP logic exactly)"""
        try:
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])
            users_collection = await get_collection(COLLECTIONS['users'])

            # Aggregate successful withdrawals by user
            pipeline = [
                {"$match": {"status": "Passed"}},
                {"$group": {
                    "_id": "$user_id",
                    "total_withdrawn": {"$sum": "$amount"},
                    "withdrawal_count": {"$sum": 1}
                }},
                {"$sort": {"total_withdrawn": -1}},
                {"$limit": limit}
            ]

            withdrawal_stats = await withdrawals_collection.aggregate(pipeline).to_list(length=limit)

            # Get user details for each user
            top_users = []
            for stat in withdrawal_stats:
                user_id = stat['_id']
                user = await users_collection.find_one({"user_id": user_id})

                if user:
                    # Get referral count
                    referral_count = len(user.get('promotion_report', []))

                    top_users.append({
                        'user_id': user_id,
                        'first_name': user.get('first_name', 'Unknown'),
                        'username': user.get('username', ''),
                        'total_withdrawn': stat['total_withdrawn'],
                        'withdrawal_count': stat['withdrawal_count'],
                        'total_referrals': referral_count,
                        'successful_withdraw': stat['total_withdrawn'],  # For compatibility
                        'banned': user.get('banned', False)
                    })

            return top_users

        except Exception as e:
            logger.error(f"Error getting top users by withdrawals: {e}")
            return []
