"""
Promotion Report Model
Handles promotion report data formatting and message generation
"""

from typing import Dict, List, Any
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

class PromotionReportModel:
    """Model for promotion report operations and formatting"""
    
    @staticmethod
    def format_promotion_report_message(
        referrals_data: Dict[str, Any],
        user_summary: Dict[str, Any]
    ) -> str:
        """
        Format promotion report message with pagination
        
        Args:
            referrals_data: Data from PromotionReportService.get_user_referrals_paginated
            user_summary: Data from PromotionReportService.get_referral_summary
            
        Returns:
            Formatted message string
        """
        if not referrals_data.get('success', False):
            return "❌ <b>Error loading promotion report</b>\n\nPlease try again later."
        
        referrals = referrals_data.get('referrals', [])
        pagination = referrals_data.get('pagination', {})
        
        # Header
        message = "📊 <b>Promotion Report</b>\n\n"
        
        # Summary statistics
        total_referrals = user_summary.get('total_referrals', 0)
        total_earnings = user_summary.get('total_earnings', 0)
        total_subordinates = user_summary.get('total_subordinates', 0)

        message += f"👥 <b>Total Referrals:</b> {total_referrals}\n"
        message += f"💰 <b>Total Earnings:</b> ₹{total_earnings}\n"
        message += f"🌐 <b>Total Subordinates:</b> {total_subordinates}\n\n"
        
        # Referrals list
        if not referrals:
            message += "❌ <b>No referrals found</b>\n\n"
            message += "Start sharing your referral link to earn rewards!"
        else:
            message += "<b>Your Referrals:</b>\n\n"
            
            for referral in referrals:
                number = referral.get('number', 0)
                user_id = referral.get('user_id', 0)
                username_display = referral.get('username_display', '@User')
                amount_got = referral.get('amount_got', 0)

                # Format with zero-padded numbering and earnings
                message += f"{number:02d}. {user_id} [{username_display}] - ₹{amount_got}\n"
        
        # Pagination info
        current_page = pagination.get('current_page', 1)
        total_pages = pagination.get('total_pages', 1)
        
        if total_pages > 1:
            message += f"\n📄 <b>Page {current_page} out of {total_pages}</b>"
        
        return message
    
    @staticmethod
    def create_promotion_report_keyboard(pagination: Dict[str, Any]) -> InlineKeyboardMarkup:
        """
        Create keyboard for promotion report with pagination
        
        Args:
            pagination: Pagination data from service
            
        Returns:
            InlineKeyboardMarkup with navigation buttons
        """
        keyboard_buttons = []
        
        # Navigation row (only if pagination is needed)
        current_page = pagination.get('current_page', 1)
        total_pages = pagination.get('total_pages', 1)
        has_previous = pagination.get('has_previous', False)
        has_next = pagination.get('has_next', False)
        
        if total_pages > 1:
            nav_row = []
            
            # Previous page button
            if has_previous:
                nav_row.append(
                    InlineKeyboardButton(
                        '◀️ Previous Page', 
                        callback_data=f'promotion_report_page_{current_page - 1}'
                    )
                )
            
            # Next page button
            if has_next:
                nav_row.append(
                    InlineKeyboardButton(
                        'Next Page ▶️', 
                        callback_data=f'promotion_report_page_{current_page + 1}'
                    )
                )
            
            if nav_row:
                keyboard_buttons.append(nav_row)
        
        # Back button (always present)
        keyboard_buttons.append([
            InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')
        ])
        
        return InlineKeyboardMarkup(keyboard_buttons)
    
    @staticmethod
    def create_empty_promotion_report_keyboard() -> InlineKeyboardMarkup:
        """
        Create keyboard for promotion report when user has no referrals
        
        Returns:
            InlineKeyboardMarkup with back button only
        """
        return InlineKeyboardMarkup([
            [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
        ])
    
    @staticmethod
    def validate_page_number(page: int, total_pages: int) -> int:
        """
        Validate and normalize page number
        
        Args:
            page: Requested page number
            total_pages: Total number of pages available
            
        Returns:
            Valid page number within range
        """
        if total_pages <= 0:
            return 1
        
        return max(1, min(page, total_pages))
    
    @staticmethod
    def format_referral_entry(
        number: int,
        user_id: int,
        username: str = None,
        first_name: str = None
    ) -> str:
        """
        Format a single referral entry for display

        Args:
            number: Sequential number for the referral
            user_id: Telegram user ID
            username: Username (optional)
            first_name: First name (optional)

        Returns:
            Formatted referral entry string
        """
        # Apply the requested username display logic:
        # 1. If user has username: Display @username in brackets
        if username and username.strip():
            username_display = f"@{username.strip()}"
        # 2. If no username but has first_name: Display first name in brackets
        elif first_name and first_name.strip():
            username_display = first_name.strip()
        # 3. If neither exists: Display "User" as fallback
        else:
            username_display = "User"

        # Format with zero-padded numbering
        return f"{number:02d}. {user_id} [{username_display}]"
    
    @staticmethod
    def get_pagination_info_text(current_page: int, total_pages: int) -> str:
        """
        Get pagination information text
        
        Args:
            current_page: Current page number
            total_pages: Total number of pages
            
        Returns:
            Formatted pagination info string
        """
        if total_pages <= 1:
            return ""
        
        return f"📄 Page {current_page} out of {total_pages}"
