"""
User service for MongoDB operations
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.user import UserModel
from utils.helpers import (
    get_current_timestamp, 
    is_valid_telegram_id,
    check_channel_membership
)

logger = logging.getLogger(__name__)

class UserService:
    """Service for user-related database operations"""
    
    def __init__(self):
        self.bot = Bot(settings.BOT_TOKEN)
    
    async def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            user = await collection.find_one({"user_id": user_id})
            return user
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None
    
    async def create_user(
        self, 
        user_id: int, 
        first_name: str, 
        last_name: str, 
        username: str, 
        referred_by: str = 'None'
    ) -> bool:
        """Create new user"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            # Check if user already exists
            existing_user = await collection.find_one({"user_id": user_id})
            if existing_user:
                return True  # User already exists
            
            # Create new user document
            user_data = UserModel.create_new_user(
                user_id, first_name, last_name, username, referred_by
            )

            # Insert into database
            result = await collection.insert_one(user_data)

            logger.info(f"Created new user: {user_id} ({first_name})")
            return result.inserted_id is not None
            
        except Exception as e:
            logger.error(f"Error creating user {user_id}: {e}")
            return False
    
    async def update_user(self, user_id: int, update_data: Dict[str, Any]) -> bool:
        """Update user data"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            # Add updated timestamp
            update_data['updated_at'] = get_current_timestamp()
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            return False
    
    async def update_user_balance(self, user_id: int, amount: int, operation: str = 'add') -> bool:
        """Update user balance"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            user = await self.get_user(user_id)
            if not user:
                return False
            
            # Update balance using model method
            updated_user = UserModel.update_user_balance(user, amount, operation)
            
            # Save to database
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "balance": updated_user['balance'],
                    "updated_at": updated_user['updated_at']
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating balance for user {user_id}: {e}")
            return False
    
    async def ban_user(self, user_id: int, banned: bool = True) -> bool:
        """Ban or unban user"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "banned": banned,
                    "updated_at": get_current_timestamp()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error banning user {user_id}: {e}")
            return False
    
    async def mark_user_referred(self, user_id: int) -> bool:
        """Mark user as having been processed for referral"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "referred": True,
                    "updated_at": get_current_timestamp()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error marking user referred {user_id}: {e}")
            return False
    
    async def update_joining_bonus(self, user_id: int, amount: int) -> bool:
        """Update joining bonus amount"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "joining_bonus_got": amount,
                    "updated_at": get_current_timestamp()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating joining bonus for user {user_id}: {e}")
            return False
    
    async def update_account_info(self, user_id: int, field: str, value: str) -> bool:
        """Update account information field"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    f"account_info.{field}": value,
                    "updated_at": get_current_timestamp()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating account info for user {user_id}: {e}")
            return False
    
    async def set_gift_claimed(self, user_id: int, claimed: bool = True) -> bool:
        """Set gift claimed status"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "gift_claimed": claimed,
                    "updated_at": get_current_timestamp()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error setting gift claimed for user {user_id}: {e}")
            return False
    
    async def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            cursor = collection.find({})
            users = await cursor.to_list(length=None)
            return users
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []
    
    async def get_total_users(self) -> int:
        """Get total number of users"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            count = await collection.count_documents({})
            return count
        except Exception as e:
            logger.error(f"Error getting total users: {e}")
            return 0

    async def get_user_referrals(self, user_id: int) -> List[Dict[str, Any]]:
        """Get list of users referred by this user"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            # Find all users referred by this user
            cursor = collection.find({"referred_by": user_id})
            referrals = await cursor.to_list(length=None)

            # Clean up the data
            for referral in referrals:
                if '_id' in referral:
                    del referral['_id']

            return referrals

        except Exception as e:
            logger.error(f"Error getting user referrals for {user_id}: {e}")
            return []

    async def get_referral_count(self, user_id: int) -> int:
        """Get count of users referred by this user"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            count = await collection.count_documents({"referred_by": user_id})
            return count

        except Exception as e:
            logger.error(f"Error getting referral count for {user_id}: {e}")
            return 0

    async def update_balance(self, user_id: int, amount: float, operation: str = 'add') -> bool:
        """Update user balance with float support"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            user = await self.get_user(user_id)
            if not user:
                return False

            current_balance = user.get('balance', 0)

            if operation == 'add':
                new_balance = current_balance + amount
            elif operation == 'subtract':
                new_balance = max(0, current_balance - amount)
            elif operation == 'set':
                new_balance = amount
            else:
                return False

            # Update balance
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "balance": new_balance,
                    "updated_at": get_current_timestamp()
                }}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating balance for user {user_id}: {e}")
            return False
    
    async def get_top_users_by_withdrawals(self, limit: int = 15) -> List[Dict[str, Any]]:
        """Get top users by withdrawal amount"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            
            # Aggregate pipeline to get top users with referral counts
            pipeline = [
                {
                    "$addFields": {
                        "total_referrals": {"$size": "$promotion_report"},
                        "withdrawal_count": {"$size": "$withdrawal_reports"}
                    }
                },
                {
                    "$match": {
                        "successful_withdraw": {"$gt": 0}
                    }
                },
                {
                    "$sort": {
                        "successful_withdraw": -1
                    }
                },
                {
                    "$limit": limit
                }
            ]
            
            cursor = collection.aggregate(pipeline)
            users = await cursor.to_list(length=None)
            return users
            
        except Exception as e:
            logger.error(f"Error getting top users by withdrawals: {e}")
            return []
    
    async def check_rate_limit(self, user_id: int) -> bool:
        """Check if user is within rate limits"""
        try:
            if not settings.RATE_LIMIT_ENABLED:
                return True
            
            collection = await get_collection(COLLECTIONS['rate_limits'])
            current_time = get_current_timestamp()
            
            # Get user's rate limit record
            rate_record = await collection.find_one({"user_id": user_id})
            
            if not rate_record:
                # Create new rate limit record
                await collection.insert_one({
                    "user_id": user_id,
                    "requests": 1,
                    "last_request": current_time,
                    "window_start": current_time
                })
                return True
            
            # Check if we're in a new minute window
            if current_time - rate_record['window_start'] >= 60:
                # Reset counter for new window
                await collection.update_one(
                    {"user_id": user_id},
                    {"$set": {
                        "requests": 1,
                        "last_request": current_time,
                        "window_start": current_time
                    }}
                )
                return True
            
            # Check if user exceeded rate limit
            max_requests = settings.MAX_REQUESTS_PER_MINUTE
            if rate_record['requests'] >= max_requests:
                return False
            
            # Increment request counter
            await collection.update_one(
                {"user_id": user_id},
                {"$inc": {"requests": 1}, "$set": {"last_request": current_time}}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit for user {user_id}: {e}")
            return True  # Allow on error
    
    async def is_maintenance_mode(self) -> bool:
        """Check if bot is in maintenance mode"""
        try:
            # Check from admin settings
            admin_collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await admin_collection.find_one({"admin_id": settings.ADMIN_ID})
            
            if admin_settings:
                maintenance_status = admin_settings.get('maintenance_status', 'Off')
                return maintenance_status.lower() == 'on'
            
            return settings.MAINTENANCE_MODE
            
        except Exception as e:
            logger.error(f"Error checking maintenance mode: {e}")
            return False
    
    async def get_bot_config(self, config_key: str, admin_id: int = None) -> Any:
        """Get bot configuration value"""
        try:
            if admin_id is None:
                admin_id = settings.ADMIN_ID
            
            admin_collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await admin_collection.find_one({"admin_id": admin_id})

            # Check if value exists in database AND is not empty
            if admin_settings and config_key in admin_settings and admin_settings[config_key]:
                return admin_settings[config_key]
            
            # Fallback to settings
            fallback_map = {
                'main_channel': settings.MAIN_CHANNEL,
                'private_logs_channel': settings.PRIVATE_LOGS_CHANNEL,
                'per_refer_amount': settings.PER_REFER_AMOUNT,
                'joining_bonus_amount': settings.JOINING_BONUS_AMOUNT,
                'gift_channel': settings.GIFT_CHANNEL,
                'gift_amount': settings.GIFT_AMOUNT
            }
            
            return fallback_map.get(config_key, '')
            
        except Exception as e:
            logger.error(f"Error getting bot config {config_key}: {e}")
            return ''
    
    async def get_force_sub_channels(self, admin_id: int = None) -> List[Dict[str, Any]]:
        """Get force subscription channels"""
        try:
            if admin_id is None:
                admin_id = settings.ADMIN_ID
            
            admin_collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await admin_collection.find_one({"admin_id": admin_id})
            
            if admin_settings:
                return admin_settings.get('force_sub_channels', [])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting force sub channels: {e}")
            return []
    
    async def check_channel_membership(self, user_id: int, channel: str) -> bool:
        """Check if user is member of a channel"""
        try:
            return await check_channel_membership(self.bot, user_id, channel)
        except Exception as e:
            logger.error(f"Error checking channel membership: {e}")
            return False
    
    async def get_bot_username(self) -> str:
        """Get bot username from database or settings"""
        try:
            bot_info_collection = await get_collection(COLLECTIONS['bot_info'])
            bot_info = await bot_info_collection.find_one({})

            if bot_info and 'username' in bot_info:
                return bot_info['username']

            return settings.BOT_USERNAME

        except Exception as e:
            logger.error(f"Error getting bot username: {e}")
            return settings.BOT_USERNAME

    async def set_withdrawal_under_review(self, user_id: int, amount: int) -> bool:
        """Set withdrawal amount under review"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "withdraw_under_review": amount,
                    "updated_at": get_current_timestamp()
                }}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error setting withdrawal under review for user {user_id}: {e}")
            return False

    async def complete_withdrawal(self, user_id: int, status: str) -> bool:
        """Complete withdrawal process (Passed or Failed)"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            # Get current user data
            user = await self.get_user(user_id)
            if not user:
                return False

            amount = user.get('withdraw_under_review', 0)
            if amount <= 0:
                return False

            update_data = {
                "withdraw_under_review": 0,
                "updated_at": get_current_timestamp()
            }

            if status == 'Passed':
                # Add to successful withdrawals
                current_successful = user.get('successful_withdraw', 0)
                update_data['successful_withdraw'] = current_successful + amount
            elif status == 'Failed':
                # Return money to balance
                current_balance = user.get('balance', 0)
                update_data['balance'] = current_balance + amount

            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error completing withdrawal for user {user_id}: {e}")
            return False

    async def add_withdrawal_report(self, user_id: int, withdrawal_record: dict) -> bool:
        """Add withdrawal report to user's history"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            result = await collection.update_one(
                {"user_id": user_id},
                {
                    "$push": {"withdrawal_reports": withdrawal_record},
                    "$set": {"updated_at": get_current_timestamp()}
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error adding withdrawal report for user {user_id}: {e}")
            return False

    async def update_withdrawal_record_status(self, user_id: int, amount: int, status: str) -> bool:
        """Update withdrawal record status in user's history"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            # Update the most recent withdrawal record with matching amount
            result = await collection.update_one(
                {
                    "user_id": user_id,
                    "withdrawal_reports.amount": amount,
                    "withdrawal_reports.status": "Under review"
                },
                {
                    "$set": {
                        "withdrawal_reports.$.status": status,
                        "updated_at": get_current_timestamp()
                    }
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating withdrawal record status for user {user_id}: {e}")
            return False

    async def update_account_info(self, user_id: int, field: str, value: str) -> bool:
        """Update account information field"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            # Validate field
            allowed_fields = ['name', 'ifsc', 'email', 'account_number', 'mobile_number', 'usdt_address', 'binance_id', 'withdrawal_method']
            if field not in allowed_fields:
                return False

            result = await collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        f"account_info.{field}": value,
                        "updated_at": get_current_timestamp()
                    }
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating account info for user {user_id}: {e}")
            return False

    async def add_claimed_gift(self, user_id: int, gift_id: str) -> bool:
        """Add claimed gift to user's record"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            result = await collection.update_one(
                {"user_id": user_id},
                {
                    "$addToSet": {"claimed_gifts": gift_id},
                    "$set": {"updated_at": get_current_timestamp()}
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error adding claimed gift for user {user_id}: {e}")
            return False

    async def reset_account_details(self, user_id: int) -> bool:
        """Reset user's account details and withdrawal method"""
        try:
            collection = await get_collection(COLLECTIONS['users'])

            # Reset account_info to empty dict
            result = await collection.update_one(
                {"user_id": user_id},
                {
                    "$unset": {
                        "account_info": ""
                    },
                    "$set": {
                        "updated_at": get_current_timestamp()
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"Reset account details for user {user_id}")
                return True
            else:
                logger.warning(f"No account details found to reset for user {user_id}")
                return True  # Consider it successful if user had no account details

        except Exception as e:
            logger.error(f"Error resetting account details for user {user_id}: {e}")
            return False

    async def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        try:
            collection = await get_collection(COLLECTIONS['users'])
            user = await collection.find_one({"username": username})
            return user
        except Exception as e:
            logger.error(f"Error getting user by username {username}: {e}")
            return None
