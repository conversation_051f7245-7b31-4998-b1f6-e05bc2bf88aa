# 🤖 Telegram Bot Admin Panel - Vercel Edition

A modern, responsive admin panel for managing your Telegram referral bot, optimized for Vercel hosting with MongoDB integration.

## ✨ Features

- 📊 **Real-time Dashboard** - Live statistics and analytics
- 👥 **User Management** - View, search, ban/unban users, manage balances
- 💰 **Withdrawal Processing** - Approve/reject withdrawal requests
- 🔗 **Referral Tracking** - Visualize referral chains and hierarchies
- 📈 **Analytics** - Charts and performance metrics
- 📱 **Mobile Responsive** - Works perfectly on all devices
- 🚀 **Vercel Hosted** - Fast, reliable, and free hosting
- 🔒 **Secure** - Environment-based configuration and authentication

## 🚀 Quick Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/telegram-bot-admin-panel)

## 📋 Prerequisites

- MongoDB database (existing bot data)
- GitHub account
- Vercel account (free)

## 🛠️ Installation

### Option 1: One-Click Deploy (Recommended)
1. Click the "Deploy with Vercel" button above
2. Connect your GitHub account
3. Set environment variables (see below)
4. Deploy!

### Option 2: Manual Setup
1. Fork this repository
2. Connect to Vercel
3. Configure environment variables
4. Deploy

## ⚙️ Environment Variables

Set these in your Vercel project settings:

```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name?retryWrites=true&w=majority
DATABASE_NAME=referral_bot
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here
```

## 📖 Detailed Setup Guide

### Step 1: Upload to GitHub
1. Create a new repository on GitHub
2. Upload all files from the `vercel-admin-panel` folder
3. Make the repository public (required for free Vercel)

### Step 2: Deploy to Vercel
1. Go to Vercel.com and sign up with GitHub
2. Click "New Project" and import your repository
3. Click "Deploy"

### Step 3: Configure Environment Variables
1. Go to your project settings in Vercel
2. Click "Environment Variables"
3. Add the four required variables listed above
4. Redeploy your project

### Step 4: Access Your Admin Panel
Your admin panel will be live at: `https://your-project-name.vercel.app`

## 🎯 Usage

1. **Access your admin panel** at your Vercel URL
2. **Login** with your admin credentials
3. **Manage users** - Search, view details, update balances
4. **Process withdrawals** - Approve or reject requests
5. **View analytics** - Monitor bot performance
6. **Export data** - Download CSV reports

## 🔧 Configuration

### MongoDB Setup
- Ensure your MongoDB allows connections from `0.0.0.0/0`
- Your bot data should be in collections: `users`, `withdrawals`
- Database structure should match your existing Python bot

### Security
- Use strong passwords for admin access
- Keep your MongoDB URI secret
- Enable MongoDB authentication
- Regular security updates

## 📊 Features Overview

### Dashboard
- Total users count
- Active vs banned users
- Pending withdrawals
- Total withdrawn amount
- Interactive charts

### User Management
- Paginated user list (15 per page)
- Search by ID, username, name, email
- View detailed user profiles
- Update user balances
- Ban/unban functionality
- Export user data to CSV

### Withdrawal Management
- Filter by status (pending/approved/rejected)
- Process withdrawal requests
- Add admin notes
- Export withdrawal data
- Search functionality

### Referral Tracking
- Visualize referral chains
- Multi-level hierarchy display
- Referral statistics
- Performance metrics

## 🔄 Updates

The admin panel automatically updates when you push changes to your GitHub repository. Vercel will redeploy automatically.

## 🐛 Troubleshooting

### Common Issues

**Database Connection Failed**
- Check MongoDB URI format
- Verify network access settings
- Ensure credentials are correct

**Login Issues**
- Verify environment variables are set
- Check username/password configuration
- Ensure project was redeployed after setting variables

**Data Not Loading**
- Confirm database collections exist
- Check collection names match expected format
- Verify MongoDB permissions

## 📁 Project Structure

```
vercel-admin-panel/
├── index.php              # Main application file
├── vercel.json            # Vercel configuration
├── composer.json          # PHP dependencies
├── package.json           # Node.js metadata
├── .env.example           # Environment variables template
├── .gitignore            # Git ignore rules
└── README.md             # This file
```

## 🛡️ Security Features

- Session-based authentication
- Environment variable configuration
- Input validation and sanitization
- Secure MongoDB queries
- HTTPS by default (Vercel)
- No sensitive data in code

## 🌟 Tech Stack

- **Backend**: PHP 8.0+
- **Database**: MongoDB
- **Frontend**: Bootstrap 5, Chart.js
- **Hosting**: Vercel Serverless Functions
- **Authentication**: Session-based
- **Icons**: Font Awesome

## 📞 Support

- Check environment variable configuration
- Verify MongoDB connection settings
- Check Vercel deployment logs
- Review the deployment guide

## 📄 License

MIT License - feel free to use for your projects!

## 🎉 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Made with ❤️ for the Telegram bot community**

Deploy your admin panel in minutes and manage your bot like a pro! 🚀
