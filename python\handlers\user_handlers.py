"""
User command handlers
Maintains identical functionality to PHP version
"""

import logging
import asyncio
import time
from typing import Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from config.settings import settings
from services.user_service import UserService
from services.referral_service import ReferralService
from utils.helpers import (
    parse_start_parameter, 
    get_welcome_message, 
    get_current_date,
    is_valid_telegram_id
)
from utils.constants import (
    BANNED_TEXT, 
    MAINTENANCE_TEXT, 
    RATE_LIMIT_EXCEEDED
)

logger = logging.getLogger(__name__)

class UserHandlers:
    """Handles user-related commands and interactions"""

    def __init__(self):
        self.user_service = UserService()
        self.referral_service = ReferralService()
        # Track active callback processing to prevent race conditions
        self._active_callbacks = set()
        # Error rate limiting to prevent log flooding
        self._error_timestamps = {}
        self._error_rate_limit = 60  # seconds between same error logs
    
    async def handle_start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command (matching PHP version exactly)"""
        user = update.effective_user
        chat_id = update.effective_chat.id
        user_id = user.id
        first_name = user.first_name or ""
        last_name = user.last_name or ""
        username = user.username or ""
        text = update.message.text
        
        try:
            # Check rate limit
            if not await self.user_service.check_rate_limit(user_id):
                await update.message.reply_text(RATE_LIMIT_EXCEEDED)
                return
            
            # Extract referral ID from /start command
            referral_id = 'None'
            referral_param = parse_start_parameter(text)
            
            if referral_param:
                # Check if it's a redeem link parameter
                if referral_param.startswith('redeem_'):
                    await self._handle_redeem_link(update, referral_param)
                    return

                # Check if it's a custom referral parameter
                custom_referral_user_id = await self.referral_service.get_user_id_by_custom_parameter(referral_param)
                if custom_referral_user_id:
                    # It's a custom parameter, use the mapped user ID
                    referral_id = custom_referral_user_id
                    logger.info(f"Custom referral parameter used: {referral_param} -> User ID: {custom_referral_user_id}")

                    # Track custom referral click
                    await self.referral_service.track_custom_referral_usage(referral_param, 'click')
                else:
                    # It's either a numeric user ID or an invalid parameter
                    referral_id = referral_param
            
            # Check if user exists
            existing_user = await self.user_service.get_user(user_id)
            
            if not existing_user:
                # Create new user
                await self.user_service.create_user(user_id, first_name, last_name, username, referral_id)
                
                # Send notification to admin logs
                await self._send_new_user_notification(user_id, first_name, username)
                
                # Notify referrer if applicable
                if referral_id != 'None' and is_valid_telegram_id(referral_id):
                    await self._notify_referrer(int(referral_id), first_name, user_id)
                
                # Reload user data
                existing_user = await self.user_service.get_user(user_id)
            
            # Check if user is banned
            if existing_user.get('banned', False):
                await update.message.reply_text(BANNED_TEXT, parse_mode='HTML')
                return
            
            # Check maintenance status
            if await self.user_service.is_maintenance_mode():
                from services.maintenance_service import MaintenanceService
                maintenance_service = MaintenanceService()

                try:
                    # Send maintenance message with image
                    maintenance_image_path = maintenance_service.get_maintenance_image_path()
                    import os

                    if os.path.exists(maintenance_image_path):
                        with open(maintenance_image_path, 'rb') as photo:
                            await update.message.reply_photo(
                                photo=photo,
                                caption=MAINTENANCE_TEXT,
                                parse_mode='HTML'
                            )
                    else:
                        # Fallback to text message if no image
                        await update.message.reply_text(MAINTENANCE_TEXT, parse_mode='HTML')
                except Exception:
                    # Fallback to text message if image fails
                    await update.message.reply_text(MAINTENANCE_TEXT, parse_mode='HTML')
                return
            
            # Send welcome message
            main_channel = await self.user_service.get_bot_config('main_channel')
            joining_bonus_amount = await self.user_service.get_bot_config('joining_bonus_amount')
            
            welcome_message = await get_welcome_message(joining_bonus_amount, main_channel)
            
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('💰𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔💰', callback_data='joined')]
            ])
            
            await update.message.reply_text(
                welcome_message,
                reply_markup=keyboard,
                parse_mode='HTML',
                disable_web_page_preview=True
            )

            # Send dynamic reminder message based on configured channels
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService(bot=context.bot)
            channels = await force_service.get_force_subscription_channels()

            if channels and len(channels) > 1:
                # Multiple channels configured
                reminder_message = "<b>💡You Must Join Our Channels Before Clicking On [💰GET MONEY💰]</b>"
            elif channels and len(channels) == 1:
                # Single channel configured
                channel = channels[0]
                channel_username = channel.get('username', main_channel)
                reminder_message = f"<b>💡You Must <a href=\"https://t.me/{channel_username}\">Join Our Channel</a> Before Clicking On [💰GET MONEY💰]</b>"
            else:
                # Fallback to main channel
                reminder_message = f"<b>💡You Must <a href=\"https://t.me/{main_channel}\">Join Our Channel</a> Before Clicking On [💰GET MONEY💰]</b>"

            await update.message.reply_text(
                reminder_message,
                parse_mode='HTML',
                disable_web_page_preview=True
            )
            
        except Exception as e:
            logger.error(f"Error in handle_start_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
    
    async def handle_debug_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /debug command (matching PHP version)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        
        try:
            debug_info = "🔧 Debug Information:\n\n"
            debug_info += "✅ Webhook is working\n"
            debug_info += f"✅ User ID: {user_id}\n"
            debug_info += f"✅ Chat ID: {chat_id}\n"
            debug_info += "✅ Storage mode: MongoDB\n"
            debug_info += "✅ Bot configured\n"
            debug_info += "\nSend /start to test the bot!"
            
            await update.message.reply_text(debug_info)
            
        except Exception as e:
            logger.error(f"Error in handle_debug_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_redeem_link(self, update: Update, referral_param: str) -> None:
        """Handle redeem link redemption"""
        user_id = update.effective_user.id

        try:
            # Extract code from parameter (remove 'redeem_' prefix)
            code = referral_param[7:]  # Remove 'redeem_' prefix

            if not code:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🎁 Extra Rewards', callback_data='extraRewards')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                await update.message.reply_text(
                    "🚫 Invalid redeem link.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Check if user exists and meets requirements
            user = await self.user_service.get_user(user_id)
            if not user:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Start Bot', url=f'https://t.me/{await self.user_service.get_bot_username()}')]
                ])

                await update.message.reply_text(
                    "❌ Please start the bot first by sending /start",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Check force join requirements if enabled
            force_join_enabled = await self.user_service.get_bot_config('force_join_enabled')
            if force_join_enabled:
                # Check if user has joined required channels
                from services.force_join_service import ForceJoinService
                force_join_service = ForceJoinService()

                if not await force_join_service.check_user_membership(user_id):
                    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('🎁 Extra Rewards', callback_data='extraRewards')],
                        [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                    ])

                    await update.message.reply_text(
                        "❌ Please join required channels first.",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                    return

            # Attempt to redeem the link code
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()

            success, message, amount = await gift_service.redeem_link_code(code, user_id)

            if success:
                # Credit user balance
                await self.user_service.update_balance(user_id, amount, 'add')

                # Get updated balance
                updated_user = await self.user_service.get_user(user_id)
                new_balance = updated_user.get('balance', 0)

                # Send success message
                success_message = f"<b>🎁 Congratulations! You've received ₹{amount} from a redeem link!</b>\n"
                success_message += f"<b>💰 Your wallet balance: ₹{new_balance:.2f}</b>"

                await update.message.reply_text(
                    success_message,
                    parse_mode='HTML'
                )

                logger.info(f"User {user_id} redeemed link code {code} for ₹{amount}")
            else:
                # Send error message with back button for navigation
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🎁 Extra Rewards', callback_data='extraRewards')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                await update.message.reply_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error handling redeem link: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while processing the redeem link. Please try again later.",
                parse_mode='HTML'
            )
    
    async def _send_new_user_notification(self, user_id: int, first_name: str, username: str) -> None:
        """Send new user notification to admin logs"""
        try:
            private_logs_channel = await self.user_service.get_bot_config('private_logs_channel')
            if not private_logs_channel:
                return
            
            total_users = await self.user_service.get_total_users()
            bot_username = await self.user_service.get_bot_username()
            
            log_message = f"""<b>🆕 New user notification

🧒 Name : {first_name}
🤫 Username : @{username}
🔗 User's link : <a href="tg://user?id={user_id}">Link</a>
🆔 User's ID :</b> <code>{user_id}</code>

<b>🤩 Total users :</b> <code>{total_users}</code>

<b>🤖 Bot : @{bot_username}</b>"""
            
            # Send to private logs channel
            from telegram import Bot
            bot = Bot(settings.BOT_TOKEN)
            await bot.send_message(
                chat_id=private_logs_channel,
                text=log_message,
                parse_mode='HTML',
                disable_web_page_preview=True
            )
            
        except Exception as e:
            logger.error(f"Error sending new user notification: {e}")
    
    async def _notify_referrer(self, referrer_id: int, referred_name: str, referred_user_id: int) -> None:
        """Notify referrer about new referral"""
        try:
            referrer_user = await self.user_service.get_user(referrer_id)
            if not referrer_user:
                logger.error(f"Referrer {referrer_id} not found for new user {referred_user_id}")
                return
            
            notification_message = f"""👏You invited <b>{referred_name}</b>!
<blockquote><b>You Will Receive Money, After He/She Joins Channel & Clicks 💰GET MONEY💰</b></blockquote>"""
            
            # Send notification to referrer
            from telegram import Bot
            bot = Bot(settings.BOT_TOKEN)
            result = await bot.send_message(
                chat_id=referrer_id,
                text=notification_message,
                parse_mode='HTML'
            )
            
            logger.info(f"Notification sent to referrer {referrer_id} about new user {referred_name} (ID: {referred_user_id}): {'success' if result else 'failed'}")
            
        except Exception as e:
            logger.error(f"Error notifying referrer {referrer_id}: {e}")
    
    def _log_error_rate_limited(self, error_key: str, message: str) -> None:
        """Log error with rate limiting to prevent log flooding"""
        current_time = time.time()
        last_logged = self._error_timestamps.get(error_key, 0)

        if current_time - last_logged >= self._error_rate_limit:
            logger.error(message)
            self._error_timestamps[error_key] = current_time
        # If within rate limit, silently ignore to prevent flooding

    def _is_message_content_different(self, query, new_text: str, new_keyboard=None) -> bool:
        """Check if the new message content is different from current message"""
        try:
            current_text = query.message.text or query.message.caption or ""
            current_keyboard = query.message.reply_markup

            # Compare text content
            if current_text.strip() != new_text.strip():
                return True

            # Compare keyboard if provided
            if new_keyboard and current_keyboard:
                # Simple comparison - convert to string and compare
                if str(current_keyboard) != str(new_keyboard):
                    return True
            elif new_keyboard != current_keyboard:  # One is None, other is not
                return True

            return False
        except Exception:
            # If we can't compare, assume it's different to be safe
            return True

    async def handle_joined_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle 'joined' callback - user claims to have joined channel"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id
        first_name = query.from_user.first_name or ""

        # Create unique callback identifier to prevent race conditions
        callback_id = f"joined_{user_id}_{message_id}"

        # Check if this callback is already being processed
        if callback_id in self._active_callbacks:
            logger.warning(f"Callback {callback_id} already being processed, ignoring duplicate")
            try:
                await query.answer("Request already being processed...", show_alert=False)
            except Exception:
                pass  # Ignore if we can't answer
            return

        # Mark callback as active
        self._active_callbacks.add(callback_id)

        try:
            # Answer callback query immediately to prevent timeout
            await query.answer("Checking subscriptions...")
            
            # Check if user exists and is not banned
            user = await self.user_service.get_user(user_id)
            if not user:
                error_text = "❌ <b>User Not Found</b>\n\nPlease send /start first."
                if self._is_message_content_different(query, error_text):
                    await query.edit_message_text(error_text, parse_mode='HTML')
                return

            if user.get('banned', False):
                if self._is_message_content_different(query, BANNED_TEXT):
                    await query.edit_message_text(BANNED_TEXT, parse_mode='HTML')
                return
            
            # Check maintenance mode
            if await self.user_service.is_maintenance_mode():
                from services.maintenance_service import MaintenanceService
                maintenance_service = MaintenanceService()

                try:
                    # Send maintenance message with image
                    maintenance_image_path = maintenance_service.get_maintenance_image_path()
                    import os
                    from telegram import InputMediaPhoto

                    if os.path.exists(maintenance_image_path):
                        # Check if current message is different before editing
                        if self._is_message_content_different(query, MAINTENANCE_TEXT):
                            with open(maintenance_image_path, 'rb') as photo:
                                await query.edit_message_media(
                                    media=InputMediaPhoto(
                                        media=photo,
                                        caption=MAINTENANCE_TEXT,
                                        parse_mode='HTML'
                                    )
                                )
                    else:
                        # Fallback to text message if no image
                        if self._is_message_content_different(query, MAINTENANCE_TEXT):
                            await query.edit_message_text(MAINTENANCE_TEXT, parse_mode='HTML')
                except Exception:
                    # Fallback to text message if image fails
                    if self._is_message_content_different(query, MAINTENANCE_TEXT):
                        await query.edit_message_text(MAINTENANCE_TEXT, parse_mode='HTML')
                return
            
            # Use force subscription service for proper verification (matching PHP logic exactly)
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService(bot=context.bot)

            # Check user subscriptions to all required channels
            subscription_status = await force_sub_service.check_user_subscriptions(user_id)

            if not subscription_status['all_subscribed']:
                # User is not subscribed to all required channels
                unsubscribed_channels = subscription_status['unsubscribed_channels']

                # Generate subscription message and keyboard
                message, keyboard = await force_sub_service.get_subscription_message_and_keyboard(unsubscribed_channels)

                # Send subscription message as NEW message (matching PHP behavior)
                await context.bot.send_message(
                    chat_id=user_id,
                    text=message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                # Don't answer again - already answered at the beginning
                return
            
            # Process joining bonus and referral reward
            await self._process_joining_rewards(user, first_name, context)

            # Send invitation message with exact PHP keyboard (matching PHP exactly)
            await self._send_invitation_message(query, user, context)

        except Exception as e:
            # Use rate-limited logging to prevent flooding
            self._log_error_rate_limited(f"joined_channel_{user_id}", f"Error in handle_joined_channel: {e}")
            error_text = "❌ <b>Error</b>\n\nSomething went wrong. Please try again later."
            try:
                if self._is_message_content_different(query, error_text):
                    await query.edit_message_text(error_text, parse_mode='HTML')
            except Exception as edit_error:
                self._log_error_rate_limited(f"edit_error_{user_id}", f"Failed to edit error message: {edit_error}")
                # If we can't edit the message, just log it
        finally:
            # Always remove callback from active set
            self._active_callbacks.discard(callback_id)
    
    async def _process_joining_rewards(self, user: dict, first_name: str, context) -> None:
        """Process joining bonus and referral rewards"""
        try:
            user_id = user['user_id']
            
            # Check if user already received joining bonus
            if user.get('joining_bonus_got', 0) > 0:
                return  # Already processed
            
            # Get bonus configuration
            joining_bonus_range = await self.user_service.get_bot_config('joining_bonus_amount_range')
            per_refer_range = await self.user_service.get_bot_config('per_refer_amount_range')
            
            # Calculate random bonus amounts
            import random
            
            # Parse ranges (format: "min-max")
            if joining_bonus_range and '-' in joining_bonus_range:
                min_bonus, max_bonus = map(int, joining_bonus_range.split('-'))
                joining_bonus = random.randint(min_bonus, max_bonus)
            else:
                joining_bonus = settings.JOINING_BONUS_AMOUNT
            
            # Give joining bonus
            if joining_bonus > 0:
                await self.user_service.update_joining_bonus(user_id, joining_bonus)
                await self.user_service.update_user_balance(user_id, joining_bonus, 'add')

                # Send joining bonus notification (matching PHP exactly)
                min_withdrawal = getattr(settings, 'MIN_WITHDRAWAL_AMOUNT', 100)
                to_withdraw = min_withdrawal - joining_bonus

                bonus_message = f"🎉Congratulation! You get ₹{joining_bonus}!\n"
                bonus_message += f"💵Withdraw still needs ₹{to_withdraw}.\n"
                bonus_message += f"Cash out in [💰My wallet]"

                # Send bonus notification as a separate message
                await context.bot.send_message(
                    chat_id=user_id,
                    text=bonus_message,
                    parse_mode='HTML'
                )
            
            # Process referral reward
            referred_by = user.get('referred_by')
            if referred_by and referred_by != 'None' and is_valid_telegram_id(referred_by):
                referrer_id = int(referred_by)
                
                # Calculate referral reward
                if per_refer_range and '-' in per_refer_range:
                    min_refer, max_refer = map(int, per_refer_range.split('-'))
                    referral_reward = random.randint(min_refer, max_refer)
                else:
                    referral_reward = settings.PER_REFER_AMOUNT
                
                if referral_reward > 0:
                    # Update referrer's balance and referral record
                    await self.referral_service.update_referral_reward(
                        referrer_id, user_id, referral_reward, first_name
                    )
            
            # Mark user as referred (processed)
            await self.user_service.mark_user_referred(user_id)

            # Track custom referral conversion if applicable
            user_data = await self.user_service.get_user(user_id)
            if user_data:
                referral_source = user_data.get('referral_source')
                if referral_source and referral_source != 'None':
                    # Check if this was from a custom referral parameter
                    custom_referral_user_id = await self.referral_service.get_user_id_by_custom_parameter(referral_source)
                    if custom_referral_user_id:
                        # Track the conversion
                        await self.referral_service.track_custom_referral_usage(referral_source, 'conversion')
                        logger.info(f"Custom referral conversion tracked: {referral_source}")
            
        except Exception as e:
            logger.error(f"Error processing joining rewards: {e}")

    async def _send_invitation_message(self, query, user: dict, context) -> None:
        """Send invitation message with invite friends and my wallet buttons (matching PHP exactly)"""
        try:
            user_id = user['user_id']
            referral_link = user.get('referral_link', '')

            # Get user-facing bonus amount for display
            user_display_bonus = getattr(settings, 'USER_DISPLAY_BONUS_MAX', 100)

            # Generate invitation message (matching PHP getInvitationMessage exactly)
            invitation_message = f"🎉 Invite your friends to get money!\n\n"
            invitation_message += f"Per Invite You Get Up To ₹{settings.USER_DISPLAY_REFERRAL_MAX}\n\n"
            invitation_message += f"🔗your invitation link(👇️Click to copy)\n\n"
            invitation_message += f"<code>✨Join me and get Up To ₹{user_display_bonus}\n{referral_link}</code>"

            # Create keyboard with exact PHP layout
            share_text = f"{referral_link}\nI've Got Up To ₹{user_display_bonus}! Click URL To Join & Make Money Now!"
            share_url = f"https://t.me/share/url?text={share_text}"

            # Check if Custom Referral Link feature is enabled
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()
            custom_referral_enabled = await custom_referral_service.get_custom_referral_system_status()

            keyboard_buttons = [
                [InlineKeyboardButton('👥 Invite friends', url=share_url)]
            ]

            # Add Custom Referral Link button if feature is enabled
            if custom_referral_enabled:
                keyboard_buttons.append([InlineKeyboardButton('🔗 Custom Referral Link', callback_data='customReferralLink')])

            keyboard_buttons.append([InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            # Send the invitation message as NEW message (matching PHP exactly)
            await context.bot.send_message(
                chat_id=user_id,
                text=invitation_message,
                reply_markup=keyboard,
                parse_mode='HTML',
                disable_web_page_preview=True
            )

        except Exception as e:
            logger.error(f"Error sending invitation message: {e}")
            # Fallback to basic message as NEW message
            await context.bot.send_message(
                chat_id=user_id,
                text="✅ <b>Welcome!</b>\n\nYou can now use the bot.",
                parse_mode='HTML'
            )
    
    async def _show_main_menu(self, query, user: dict) -> None:
        """Show main menu to user (matching PHP version exactly)"""
        try:
            balance = user.get('balance', 0)
            referral_link = user.get('referral_link', '')

            # Get referral statistics
            from services.referral_service import ReferralService
            referral_service = ReferralService()
            referral_stats = await referral_service.get_referral_statistics(user['user_id'])

            total_referrals = referral_stats['total_referrals']
            total_referral_earnings = referral_stats['total_earnings']

            menu_text = f"""🎉 <b>Welcome to the Bot!</b>

💰 <b>Your Balance:</b> ₹{balance}
👥 <b>Total Referrals:</b> {total_referrals}
💰 <b>Referral Earnings:</b> ₹{total_referral_earnings}

🔗 <b>Your Referral Link:</b>
<code>{referral_link}</code>

Choose an option below:"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')],
                [InlineKeyboardButton('💸 Cash Out', callback_data='cashOut')],
                [InlineKeyboardButton('⚙️ Set Account Info', callback_data='setAccountInfo')],
                [InlineKeyboardButton('📊 Promotion Report', callback_data='promotionReport')],
                [InlineKeyboardButton('📋 Withdrawal Record', callback_data='withdrawalRecord')],
                [InlineKeyboardButton('🎁 Extra Rewards', callback_data='extraRewards')]
            ])

            await query.edit_message_text(
                menu_text,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error showing main menu: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
