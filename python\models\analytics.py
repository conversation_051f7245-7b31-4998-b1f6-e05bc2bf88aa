"""
Analytics model for formatting statistics and reports
Maintains identical functionality to PHP version
"""

import logging
from typing import Dict, Any, List
from datetime import datetime
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

logger = logging.getLogger(__name__)

class AnalyticsModel:
    """Model for analytics operations"""
    
    @staticmethod
    def format_comprehensive_statistics_message(stats: Dict[str, Any]) -> str:
        """Format comprehensive statistics message for admin (matching PHP version exactly)"""
        try:
            message = "📊 <b>Bot Statistics Dashboard</b>\n\n"
            
            # User Statistics
            message += "👥 <b>User Statistics:</b>\n"
            message += f"• Total Users: <b>{stats.get('total_users', 0):,}</b>\n"
            message += f"• Active Users: <b>{stats.get('active_users', 0):,}</b>\n"
            message += f"• Banned Users: <b>{stats.get('banned_users', 0):,}</b>\n"
            message += f"• Users Today: <b>{stats.get('users_today', 0):,}</b>\n"
            message += f"• Users This Week: <b>{stats.get('users_this_week', 0):,}</b>\n"
            message += f"• Users This Month: <b>{stats.get('users_this_month', 0):,}</b>\n"
            message += f"• Users with Balance: <b>{stats.get('users_with_balance', 0):,}</b>\n\n"
            
            # Financial Statistics
            message += "💰 <b>Financial Statistics:</b>\n"
            message += f"• Total Balance: <b>₹{stats.get('total_balance', 0):,.2f}</b>\n"
            message += f"• Total Withdrawals: <b>{stats.get('total_withdrawals', 0):,}</b>\n"
            message += f"• Pending Withdrawals: <b>{stats.get('pending_withdrawals', 0):,}</b>\n"
            message += f"• Approved Withdrawals: <b>{stats.get('approved_withdrawals', 0):,}</b>\n"
            message += f"• Total Withdrawn: <b>₹{stats.get('total_withdrawn', 0):,.2f}</b>\n"
            message += f"• Pending Amount: <b>₹{stats.get('pending_amount', 0):,.2f}</b>\n"
            message += f"• Withdrawals Today: <b>{stats.get('withdrawals_today', 0):,}</b>\n\n"
            
            # Task Statistics
            message += "📋 <b>Task Statistics:</b>\n"
            message += f"• Total Tasks: <b>{stats.get('total_tasks', 0):,}</b>\n"
            message += f"• Active Tasks: <b>{stats.get('active_tasks', 0):,}</b>\n"
            message += f"• Total Submissions: <b>{stats.get('total_submissions', 0):,}</b>\n"
            message += f"• Pending Submissions: <b>{stats.get('pending_submissions', 0):,}</b>\n"
            message += f"• Approved Submissions: <b>{stats.get('approved_submissions', 0):,}</b>\n"
            message += f"• Rejected Submissions: <b>{stats.get('rejected_submissions', 0):,}</b>\n"
            message += f"• Submissions Today: <b>{stats.get('submissions_today', 0):,}</b>\n\n"
            
            # Referral Statistics
            message += "🔗 <b>Referral Statistics:</b>\n"
            message += f"• Total Referrals: <b>{stats.get('total_referrals', 0):,}</b>\n"
            message += f"• Users with Referrals: <b>{stats.get('users_with_referrals', 0):,}</b>\n"
            message += f"• Custom Referral Links: <b>{stats.get('total_custom_referrals', 0):,}</b>\n"
            message += f"• Custom Link Clicks: <b>{stats.get('total_custom_clicks', 0):,}</b>\n"
            message += f"• Custom Conversions: <b>{stats.get('total_custom_conversions', 0):,}</b>\n\n"
            
            # System Statistics
            message += "⚙️ <b>System Statistics:</b>\n"
            message += f"• Force Channels: <b>{stats.get('total_force_channels', 0):,}</b>\n"
            message += f"• Gift Codes: <b>{stats.get('total_gift_codes', 0):,}</b>\n"
            message += f"• Active Gift Codes: <b>{stats.get('active_gift_codes', 0):,}</b>\n"
            message += f"• Admin Actions: <b>{stats.get('total_admin_actions', 0):,}</b>\n"
            message += f"• Admin Actions Today: <b>{stats.get('admin_actions_today', 0):,}</b>\n\n"
            
            # Last updated
            last_updated = stats.get('last_updated', 0)
            if last_updated:
                update_time = datetime.fromtimestamp(last_updated).strftime('%Y-%m-%d %H:%M:%S')
                message += f"🕒 <i>Last updated: {update_time}</i>"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting comprehensive statistics: {e}")
            return "📊 <b>Bot Statistics Dashboard</b>\n\n❌ Error loading statistics."
    
    @staticmethod
    def format_daily_statistics_message(daily_stats: List[Dict[str, Any]]) -> str:
        """Format daily statistics message (matching PHP version exactly)"""
        try:
            if not daily_stats:
                return "📈 <b>Daily Statistics</b>\n\n❌ No data available."
            
            message = "📈 <b>Daily Statistics (Last 7 Days)</b>\n\n"
            
            for day_stat in daily_stats:
                date = day_stat.get('date', 'Unknown')
                day_name = day_stat.get('day_name', 'Unknown')
                new_users = day_stat.get('new_users', 0)
                new_withdrawals = day_stat.get('new_withdrawals', 0)
                withdrawal_amount = day_stat.get('withdrawal_amount', 0)
                new_submissions = day_stat.get('new_submissions', 0)
                
                message += f"📅 <b>{day_name}, {date}</b>\n"
                message += f"   👥 New Users: {new_users:,}\n"
                message += f"   💸 Withdrawals: {new_withdrawals:,} (₹{withdrawal_amount:,.2f})\n"
                message += f"   📋 Submissions: {new_submissions:,}\n\n"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting daily statistics: {e}")
            return "📈 <b>Daily Statistics</b>\n\n❌ Error loading daily statistics."
    
    @staticmethod
    def format_top_performers_message(performers: Dict[str, Any]) -> str:
        """Format top performers message (matching PHP version exactly)"""
        try:
            message = "🏆 <b>Top Performers</b>\n\n"
            
            # Top users by balance
            top_balance = performers.get('top_balance', [])
            if top_balance:
                message += "💰 <b>Top Users by Balance:</b>\n"
                for i, user in enumerate(top_balance[:5], 1):
                    name = user.get('first_name', 'Unknown')
                    balance = user.get('balance', 0)
                    user_id = user.get('user_id', 'Unknown')
                    message += f"{i}. {name} (ID: {user_id}) - ₹{balance:,.2f}\n"
                message += "\n"
            
            # Top users by referrals
            top_referrals = performers.get('top_referrals', [])
            if top_referrals:
                message += "🔗 <b>Top Users by Referrals:</b>\n"
                for i, user in enumerate(top_referrals[:5], 1):
                    name = user.get('first_name', 'Unknown')
                    referral_count = user.get('referral_count', 0)
                    user_id = user.get('user_id', 'Unknown')
                    message += f"{i}. {name} (ID: {user_id}) - {referral_count:,} referrals\n"
                message += "\n"
            
            # Top users by submissions
            top_submitters = performers.get('top_submitters', [])
            if top_submitters:
                message += "📋 <b>Top Users by Task Submissions:</b>\n"
                for i, user in enumerate(top_submitters[:5], 1):
                    name = user.get('first_name', 'Unknown')
                    submission_count = user.get('submission_count', 0)
                    user_id = user.get('user_id', 'Unknown')
                    message += f"{i}. {name} (ID: {user_id}) - {submission_count:,} submissions\n"
                message += "\n"
            
            if not any([top_balance, top_referrals, top_submitters]):
                message += "❌ No performance data available."
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting top performers: {e}")
            return "🏆 <b>Top Performers</b>\n\n❌ Error loading performance data."
    
    @staticmethod
    def format_growth_metrics_message(growth: Dict[str, Any]) -> str:
        """Format growth metrics message (matching PHP version exactly)"""
        try:
            message = "📈 <b>Growth Metrics</b>\n\n"
            
            # Monthly growth
            users_this_month = growth.get('users_this_month', 0)
            users_last_month = growth.get('users_last_month', 0)
            monthly_growth_rate = growth.get('monthly_growth_rate', 0)
            
            growth_emoji = "📈" if monthly_growth_rate >= 0 else "📉"
            growth_sign = "+" if monthly_growth_rate >= 0 else ""
            
            message += "📅 <b>Monthly Growth:</b>\n"
            message += f"• This Month: <b>{users_this_month:,}</b> users\n"
            message += f"• Last Month: <b>{users_last_month:,}</b> users\n"
            message += f"• Growth Rate: <b>{growth_sign}{monthly_growth_rate}%</b> {growth_emoji}\n\n"
            
            # Weekly growth
            users_this_week = growth.get('users_this_week', 0)
            users_last_week = growth.get('users_last_week', 0)
            weekly_growth_rate = growth.get('weekly_growth_rate', 0)
            
            weekly_emoji = "📈" if weekly_growth_rate >= 0 else "📉"
            weekly_sign = "+" if weekly_growth_rate >= 0 else ""
            
            message += "📅 <b>Weekly Growth:</b>\n"
            message += f"• This Week: <b>{users_this_week:,}</b> users\n"
            message += f"• Last Week: <b>{users_last_week:,}</b> users\n"
            message += f"• Growth Rate: <b>{weekly_sign}{weekly_growth_rate}%</b> {weekly_emoji}\n\n"
            
            # Growth trend analysis
            if monthly_growth_rate > 10:
                message += "🚀 <b>Excellent growth!</b> Your bot is expanding rapidly."
            elif monthly_growth_rate > 0:
                message += "✅ <b>Positive growth!</b> Your bot is growing steadily."
            elif monthly_growth_rate == 0:
                message += "➖ <b>Stable user base.</b> Consider marketing strategies."
            else:
                message += "⚠️ <b>Declining growth.</b> Review your bot strategy."
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting growth metrics: {e}")
            return "📈 <b>Growth Metrics</b>\n\n❌ Error loading growth data."
    
    @staticmethod
    def create_analytics_dashboard_keyboard() -> InlineKeyboardMarkup:
        """Create analytics dashboard keyboard (matching PHP version exactly)"""
        try:
            keyboard_buttons = [
                [
                    InlineKeyboardButton('📊 Overview', callback_data='analytics_overview'),
                    InlineKeyboardButton('📈 Daily Stats', callback_data='analytics_daily')
                ],
                [
                    InlineKeyboardButton('🏆 Top Performers', callback_data='analytics_performers'),
                    InlineKeyboardButton('📈 Growth Metrics', callback_data='analytics_growth')
                ],
                [
                    InlineKeyboardButton('💰 Financial Report', callback_data='analytics_financial'),
                    InlineKeyboardButton('📋 Task Report', callback_data='analytics_tasks')
                ],
                [
                    InlineKeyboardButton('🔗 Referral Report', callback_data='analytics_referrals'),
                    InlineKeyboardButton('⚙️ System Report', callback_data='analytics_system')
                ],
                [
                    InlineKeyboardButton('🔄 Refresh Data', callback_data='analytics_refresh'),
                    InlineKeyboardButton('↩️ Back to Admin', callback_data='admin')
                ]
            ]
            
            return InlineKeyboardMarkup(keyboard_buttons)
            
        except Exception as e:
            logger.error(f"Error creating analytics dashboard keyboard: {e}")
            # Return basic keyboard if there's an error
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])
    
    @staticmethod
    def create_analytics_navigation_keyboard() -> InlineKeyboardMarkup:
        """Create analytics navigation keyboard (matching PHP version exactly)"""
        try:
            keyboard_buttons = [
                [
                    InlineKeyboardButton('📊 Back to Dashboard', callback_data='analytics_overview'),
                    InlineKeyboardButton('🔄 Refresh', callback_data='analytics_refresh')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
                ]
            ]
            
            return InlineKeyboardMarkup(keyboard_buttons)
            
        except Exception as e:
            logger.error(f"Error creating analytics navigation keyboard: {e}")
            # Return basic keyboard if there's an error
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])
