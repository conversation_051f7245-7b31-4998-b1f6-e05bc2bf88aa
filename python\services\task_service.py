"""
Task service for managing tasks and submissions
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from telegram import Bo<PERSON>

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.task import TaskModel, TaskSubmissionModel, TaskStatsModel
from utils.helpers import get_current_timestamp, send_safe_message, get_all_admin_ids

logger = logging.getLogger(__name__)

class TaskService:
    """Service for task-related operations"""
    
    def __init__(self):
        self.bot = Bot(settings.BOT_TOKEN)
    
    async def get_all_tasks(self) -> List[Dict[str, Any]]:
        """Get all tasks"""
        try:
            collection = await get_collection(COLLECTIONS['tasks'])
            cursor = collection.find({})
            tasks = await cursor.to_list(length=None)
            return tasks
        except Exception as e:
            logger.error(f"Error getting all tasks: {e}")
            return []
    
    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """Get active tasks only"""
        try:
            collection = await get_collection(COLLECTIONS['tasks'])
            cursor = collection.find({"status": "active"})
            tasks = await cursor.to_list(length=None)
            return tasks
        except Exception as e:
            logger.error(f"Error getting active tasks: {e}")
            return []
    
    async def get_task_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        try:
            collection = await get_collection(COLLECTIONS['tasks'])
            task = await collection.find_one({"task_id": task_id})
            return task
        except Exception as e:
            logger.error(f"Error getting task {task_id}: {e}")
            return None

    async def get_user_submissions(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all submissions by a user"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            cursor = collection.find({"user_id": user_id})
            submissions = await cursor.to_list(length=None)
            return submissions
        except Exception as e:
            logger.error(f"Error getting user submissions for {user_id}: {e}")
            return []
    
    async def add_task(self, task_data: Dict[str, Any]) -> bool:
        """Add new task"""
        try:
            collection = await get_collection(COLLECTIONS['tasks'])
            
            # Validate task data
            if not TaskModel.validate_task_data(task_data):
                logger.error("Invalid task data")
                return False
            
            result = await collection.insert_one(task_data)
            
            if result.inserted_id:
                logger.info(f"Created new task: {task_data['task_id']} ({task_data['name']})")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error adding task: {e}")
            return False
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """Update task"""
        try:
            collection = await get_collection(COLLECTIONS['tasks'])
            
            # Add updated timestamp
            updates['updated_at'] = get_current_timestamp()
            
            result = await collection.update_one(
                {"task_id": task_id},
                {"$set": updates}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {e}")
            return False
    
    async def delete_task(self, task_id: str) -> bool:
        """Delete task"""
        try:
            collection = await get_collection(COLLECTIONS['tasks'])
            
            result = await collection.delete_one({"task_id": task_id})
            
            if result.deleted_count > 0:
                # Also delete related submissions
                await self.delete_task_submissions(task_id)
                logger.info(f"Deleted task: {task_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {e}")
            return False
    
    async def get_all_task_submissions(self) -> List[Dict[str, Any]]:
        """Get all task submissions"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            cursor = collection.find({})
            submissions = await cursor.to_list(length=None)
            return submissions
        except Exception as e:
            logger.error(f"Error getting all task submissions: {e}")
            return []
    
    async def get_pending_task_submissions(self) -> List[Dict[str, Any]]:
        """Get pending task submissions"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            cursor = collection.find({"status": "pending"}).sort("submitted_at", 1)
            submissions = await cursor.to_list(length=None)
            return submissions
        except Exception as e:
            logger.error(f"Error getting pending task submissions: {e}")
            return []
    
    async def get_user_task_submissions(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user's task submissions"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            cursor = collection.find({"user_id": user_id}).sort("submitted_at", -1)
            submissions = await cursor.to_list(length=None)
            return submissions
        except Exception as e:
            logger.error(f"Error getting user task submissions for {user_id}: {e}")
            return []
    
    async def get_submission_by_id(self, submission_id: str) -> Optional[Dict[str, Any]]:
        """Get submission by ID"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            submission = await collection.find_one({"submission_id": submission_id})
            return submission
        except Exception as e:
            logger.error(f"Error getting submission {submission_id}: {e}")
            return None
    
    async def add_task_submission(self, submission_data: Dict[str, Any]) -> Optional[str]:
        """Add task submission"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            
            # Validate submission data
            if not TaskSubmissionModel.validate_submission_data(submission_data):
                logger.error("Invalid submission data")
                return None
            
            result = await collection.insert_one(submission_data)
            
            if result.inserted_id:
                submission_id = submission_data['submission_id']
                logger.info(f"Created new task submission: {submission_id}")
                return submission_id
            
            return None
            
        except Exception as e:
            logger.error(f"Error adding task submission: {e}")
            return None
    
    async def update_task_submission_status(
        self, 
        submission_id: str, 
        status: str, 
        admin_note: str = ""
    ) -> bool:
        """Update task submission status"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            
            update_data = {
                "status": status,
                "admin_note": admin_note,
                "reviewed_at": get_current_timestamp()
            }
            
            result = await collection.update_one(
                {"submission_id": submission_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating submission status {submission_id}: {e}")
            return False
    
    async def delete_task_submissions(self, task_id: str) -> bool:
        """Delete all submissions for a task"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            
            result = await collection.delete_many({"task_id": task_id})
            
            logger.info(f"Deleted {result.deleted_count} submissions for task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting submissions for task {task_id}: {e}")
            return False

    async def get_task_submission_count(self, task_id: str) -> int:
        """Get total submission count for a task"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])

            count = await collection.count_documents({"task_id": task_id})
            return count

        except Exception as e:
            logger.error(f"Error getting submission count for task {task_id}: {e}")
            return 0

    async def get_pending_submissions_count(self, task_id: str) -> int:
        """Get pending submission count for a task"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])

            count = await collection.count_documents({
                "task_id": task_id,
                "status": "pending"
            })
            return count

        except Exception as e:
            logger.error(f"Error getting pending submission count for task {task_id}: {e}")
            return 0

    async def get_approved_submissions_count(self, task_id: str) -> int:
        """Get approved submission count for a task"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])

            count = await collection.count_documents({
                "task_id": task_id,
                "status": "approved"
            })
            return count

        except Exception as e:
            logger.error(f"Error getting approved submission count for task {task_id}: {e}")
            return 0

    async def get_rejected_submissions_count(self, task_id: str) -> int:
        """Get rejected submission count for a task"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])

            count = await collection.count_documents({
                "task_id": task_id,
                "status": "rejected"
            })
            return count

        except Exception as e:
            logger.error(f"Error getting rejected submission count for task {task_id}: {e}")
            return 0
    
    async def check_user_task_submission(self, user_id: int, task_id: str) -> Optional[Dict[str, Any]]:
        """Check if user has submitted a specific task"""
        try:
            collection = await get_collection(COLLECTIONS['task_submissions'])
            submission = await collection.find_one({
                "user_id": user_id,
                "task_id": task_id
            })
            return submission
        except Exception as e:
            logger.error(f"Error checking user task submission: {e}")
            return None
    
    async def approve_task_submission(self, submission_id: str, admin_note: str = "") -> bool:
        """Approve task submission and reward user"""
        try:
            # Get submission details
            submission = await self.get_submission_by_id(submission_id)
            if not submission:
                logger.error(f"Submission {submission_id} not found")
                return False
            
            # Get task details
            task = await self.get_task_by_id(submission['task_id'])
            if not task:
                logger.error(f"Task {submission['task_id']} not found")
                return False
            
            # Update submission status
            if not await self.update_task_submission_status(submission_id, 'approved', admin_note):
                return False
            
            # Add reward to user balance
            from services.user_service import UserService
            user_service = UserService()
            
            reward_amount = task['reward_amount']
            user_id = submission['user_id']
            
            if await user_service.update_user_balance(user_id, reward_amount, 'add'):
                # Notify user
                await self._notify_user_task_approved(user_id, task, reward_amount)
                
                logger.info(f"Task submission approved: {submission_id}, user {user_id} rewarded ₹{reward_amount}")
                return True
            else:
                # Rollback submission status if balance update failed
                await self.update_task_submission_status(submission_id, 'pending', "")
                logger.error(f"Failed to update user balance for submission {submission_id}")
                return False
            
        except Exception as e:
            logger.error(f"Error approving task submission {submission_id}: {e}")
            return False
    
    async def reject_task_submission(self, submission_id: str, admin_note: str = "") -> bool:
        """Reject task submission"""
        try:
            # Get submission details
            submission = await self.get_submission_by_id(submission_id)
            if not submission:
                logger.error(f"Submission {submission_id} not found")
                return False
            
            # Get task details
            task = await self.get_task_by_id(submission['task_id'])
            if not task:
                logger.error(f"Task {submission['task_id']} not found")
                return False
            
            # Update submission status
            if await self.update_task_submission_status(submission_id, 'rejected', admin_note):
                # Notify user
                await self._notify_user_task_rejected(submission['user_id'], task, admin_note)
                
                logger.info(f"Task submission rejected: {submission_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error rejecting task submission {submission_id}: {e}")
            return False
    
    async def _notify_user_task_approved(self, user_id: int, task: Dict[str, Any], reward_amount: int) -> None:
        """Notify user that task was approved"""
        try:
            message = f"""✅ <b>Task Approved!</b>

📝 <b>Task:</b> {task['name']}
💰 <b>Reward:</b> ₹{reward_amount}

🎉 Your task submission has been approved!
💳 ₹{reward_amount} has been added to your balance."""
            
            await send_safe_message(self.bot, user_id, message, parse_mode='HTML')
            
        except Exception as e:
            logger.error(f"Error notifying user task approved: {e}")
    
    async def _notify_user_task_rejected(self, user_id: int, task: Dict[str, Any], admin_note: str = "") -> None:
        """Notify user that task was rejected"""
        try:
            message = f"""❌ <b>Task Rejected</b>

📝 <b>Task:</b> {task['name']}
💰 <b>Reward:</b> ₹{task['reward_amount']}

😔 Your task submission has been rejected."""
            
            if admin_note:
                message += f"\n\n📋 <b>Admin Note:</b> {admin_note}"
            
            message += "\n\n💬 Please contact our support team for more information."
            
            await send_safe_message(self.bot, user_id, message, parse_mode='HTML')
            
        except Exception as e:
            logger.error(f"Error notifying user task rejected: {e}")
    
    async def notify_admins_new_submission(
        self,
        submission: Dict[str, Any],
        task: Dict[str, Any],
        user: Dict[str, Any],
        file_id: str
    ) -> None:
        """Notify configured admin about new task submission"""
        try:
            from config.settings import settings

            # Use configured task notification admin ID, fallback to all admins if not configured
            task_admin_id = settings.TASK_NOTIFICATION_ADMIN_ID
            if task_admin_id and task_admin_id != 0:
                admin_ids = [task_admin_id]
            else:
                # Fallback to all admins if not configured
                admin_ids = get_all_admin_ids()
            
            admin_message = f"""📋 <b>New Task Submission</b>

👤 <b>User:</b> {user['first_name']} (ID: {submission['user_id']})
📋 <b>Task:</b> {task['name']}
💰 <b>Reward:</b> ₹{task['reward_amount']}
🆔 <b>Submission ID:</b> {submission['submission_id']}"""
            
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            
            admin_keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Approve', callback_data=f"approveTask_{submission['submission_id']}"),
                    InlineKeyboardButton('❌ Reject', callback_data=f"rejectTask_{submission['submission_id']}")
                ]
            ])
            
            for admin_id in admin_ids:
                try:
                    # Forward the photo/document to admin
                    if file_id:
                        # Determine if it's a photo or document and forward accordingly
                        if file_id.startswith('AgAC'):  # Photo file_id pattern
                            await self.bot.send_photo(
                                chat_id=admin_id,
                                photo=file_id,
                                caption=admin_message,
                                parse_mode='HTML',
                                reply_markup=admin_keyboard
                            )
                        else:  # Document
                            await self.bot.send_document(
                                chat_id=admin_id,
                                document=file_id,
                                caption=admin_message,
                                parse_mode='HTML',
                                reply_markup=admin_keyboard
                            )
                    else:
                        # Just send the message if no file
                        await send_safe_message(
                            self.bot,
                            admin_id,
                            admin_message,
                            reply_markup=admin_keyboard,
                            parse_mode='HTML'
                        )
                        
                except Exception as e:
                    logger.error(f"Failed to notify admin {admin_id}: {e}")
            
        except Exception as e:
            logger.error(f"Error notifying admins about new submission: {e}")
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """Get task statistics"""
        try:
            tasks = await self.get_all_tasks()
            submissions = await self.get_all_task_submissions()
            
            return TaskStatsModel.calculate_task_stats(tasks, submissions)
            
        except Exception as e:
            logger.error(f"Error getting task statistics: {e}")
            return {
                'total_tasks': 0,
                'active_tasks': 0,
                'inactive_tasks': 0,
                'total_submissions': 0,
                'pending_submissions': 0,
                'approved_submissions': 0,
                'rejected_submissions': 0,
                'total_rewards_distributed': 0
            }
