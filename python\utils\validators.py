"""
Input validation utilities
Maintains identical validation logic to PHP version
"""

import re
from typing import Union, Optional
from utils.helpers import (
    is_valid_telegram_id,
    is_valid_custom_parameter,
    is_valid_email,
    is_valid_mobile_number,
    is_valid_ifsc_code,
    is_valid_account_number,
    is_valid_usdt_address
)

def validate_user_id(user_id: Union[str, int]) -> bool:
    """Validate Telegram user ID"""
    return is_valid_telegram_id(user_id)

def validate_amount(amount: Union[str, int, float]) -> bool:
    """Validate monetary amount"""
    try:
        amount_val = float(amount)
        return amount_val >= 0 and amount_val <= 999999
    except (ValueError, TypeError):
        return False

def validate_custom_referral_parameter(param: str) -> bool:
    """Validate custom referral parameter"""
    return is_valid_custom_parameter(param)

def validate_email_address(email: str) -> bool:
    """Validate email address"""
    return is_valid_email(email)

def validate_mobile_number(mobile: str) -> bool:
    """Validate mobile number"""
    return is_valid_mobile_number(mobile)

def validate_ifsc_code(ifsc: str) -> bool:
    """Validate IFSC code"""
    return is_valid_ifsc_code(ifsc)

def validate_account_number(account_number: str) -> bool:
    """Validate bank account number"""
    return is_valid_account_number(account_number)

def validate_usdt_address(address: str) -> bool:
    """Validate USDT address"""
    return is_valid_usdt_address(address)

def validate_channel_username(username: str) -> bool:
    """Validate Telegram channel username"""
    if not username or not isinstance(username, str):
        return False
    
    # Remove @ if present
    username = username.lstrip('@')
    
    # Channel username should be 5-32 characters, alphanumeric + underscores
    pattern = r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$'
    return bool(re.match(pattern, username))

def validate_otp_code(otp: str) -> bool:
    """Validate OTP code"""
    if not otp or not isinstance(otp, str):
        return False
    
    # OTP should be 4-8 digits
    pattern = r'^\d{4,8}$'
    return bool(re.match(pattern, otp))

def validate_gift_code(code: str) -> bool:
    """Validate gift code format"""
    if not code or not isinstance(code, str):
        return False
    
    # Gift code should be 6-20 characters, alphanumeric
    pattern = r'^[a-zA-Z0-9]{6,20}$'
    return bool(re.match(pattern, code))

def validate_task_name(name: str) -> bool:
    """Validate task name"""
    if not name or not isinstance(name, str):
        return False
    
    # Task name should be 3-100 characters
    return 3 <= len(name.strip()) <= 100

def validate_task_description(description: str) -> bool:
    """Validate task description"""
    if not description or not isinstance(description, str):
        return False
    
    # Task description should be 10-2000 characters
    return 10 <= len(description.strip()) <= 2000

def validate_url(url: str) -> bool:
    """Validate URL format"""
    if not url or not isinstance(url, str):
        return False
    
    # Basic URL validation
    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return bool(re.match(pattern, url))

def validate_withdrawal_method(method: str) -> bool:
    """Validate withdrawal method"""
    valid_methods = ['bank', 'usdt']
    return method in valid_methods

def validate_tax_type(tax_type: str) -> bool:
    """Validate withdrawal tax type"""
    valid_types = ['none', 'fixed', 'percentage']
    return tax_type in valid_types

def validate_percentage(percentage: Union[str, int, float]) -> bool:
    """Validate percentage value (0-100)"""
    try:
        pct_val = float(percentage)
        return 0 <= pct_val <= 100
    except (ValueError, TypeError):
        return False

def validate_maintenance_status(status: str) -> bool:
    """Validate maintenance status"""
    valid_statuses = ['on', 'off', 'On', 'Off']
    return status in valid_statuses

def validate_broadcast_type(broadcast_type: str) -> bool:
    """Validate broadcast type"""
    valid_types = ['text', 'gift', 'enhanced']
    return broadcast_type in valid_types

def validate_task_status(status: str) -> bool:
    """Validate task status"""
    valid_statuses = ['active', 'inactive', 'completed']
    return status in valid_statuses

def validate_submission_status(status: str) -> bool:
    """Validate task submission status"""
    valid_statuses = ['pending', 'approved', 'rejected']
    return status in valid_statuses

def validate_withdrawal_status(status: str) -> bool:
    """Validate withdrawal status"""
    valid_statuses = ['Passed', 'Failed', 'Pending']
    return status in valid_statuses

def validate_level_number(level: Union[str, int]) -> bool:
    """Validate level number"""
    try:
        level_val = int(level)
        return 1 <= level_val <= 50  # Reasonable level range
    except (ValueError, TypeError):
        return False

def validate_referral_requirement(requirement: Union[str, int]) -> bool:
    """Validate referral requirement for level"""
    try:
        req_val = int(requirement)
        return 0 <= req_val <= 10000  # Reasonable referral range
    except (ValueError, TypeError):
        return False

def validate_bonus_amount(amount: Union[str, int, float]) -> bool:
    """Validate bonus amount"""
    try:
        amount_val = float(amount)
        return 0 <= amount_val <= 100000  # Reasonable bonus range
    except (ValueError, TypeError):
        return False

def validate_text_length(text: str, min_length: int = 1, max_length: int = 4096) -> bool:
    """Validate text length"""
    if not text or not isinstance(text, str):
        return False
    
    text_length = len(text.strip())
    return min_length <= text_length <= max_length

def validate_positive_integer(value: Union[str, int]) -> bool:
    """Validate positive integer"""
    try:
        int_val = int(value)
        return int_val > 0
    except (ValueError, TypeError):
        return False

def validate_non_negative_integer(value: Union[str, int]) -> bool:
    """Validate non-negative integer"""
    try:
        int_val = int(value)
        return int_val >= 0
    except (ValueError, TypeError):
        return False

def validate_range_string(range_str: str) -> bool:
    """Validate range string format (e.g., '1-10')"""
    if not range_str or not isinstance(range_str, str):
        return False
    
    pattern = r'^\d+-\d+$'
    if not re.match(pattern, range_str):
        return False
    
    try:
        min_val, max_val = map(int, range_str.split('-'))
        return min_val <= max_val and min_val >= 0
    except (ValueError, TypeError):
        return False

def sanitize_input(text: str, max_length: int = 1000) -> str:
    """Sanitize user input"""
    if not text or not isinstance(text, str):
        return ""
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    # Truncate if too long
    if len(text) > max_length:
        text = text[:max_length]
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<script', '</script', 'javascript:', 'data:', 'vbscript:']
    text_lower = text.lower()
    
    for dangerous in dangerous_chars:
        if dangerous in text_lower:
            text = text.replace(dangerous, '')
    
    return text

def validate_file_type(file_name: str, allowed_types: list = None) -> bool:
    """Validate file type by extension"""
    if not file_name or not isinstance(file_name, str):
        return False
    
    if allowed_types is None:
        allowed_types = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx']
    
    file_extension = file_name.lower().split('.')[-1] if '.' in file_name else ''
    return f'.{file_extension}' in allowed_types

def validate_image_file(file_name: str) -> bool:
    """Validate image file type"""
    image_types = ['.jpg', '.jpeg', '.png', '.gif']
    return validate_file_type(file_name, image_types)

def validate_document_file(file_name: str) -> bool:
    """Validate document file type"""
    document_types = ['.pdf', '.doc', '.docx', '.txt']
    return validate_file_type(file_name, document_types)
