"""
Custom Referral service for managing custom referral links
Maintains identical functionality to PHP version
"""

import logging
import re
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class CustomReferralService:
    """Service for custom referral operations"""
    
    def __init__(self):
        pass
    
    async def get_all_custom_referrals(self) -> List[Dict[str, Any]]:
        """Get all custom referral links (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            cursor = collection.find({})
            referrals = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for referral in referrals:
                if '_id' in referral:
                    del referral['_id']
            
            return referrals
            
        except Exception as e:
            logger.error(f"Error getting custom referrals: {e}")
            return []
    
    async def get_custom_referral_by_param(self, param: str) -> Optional[Dict[str, Any]]:
        """Get custom referral by parameter"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            referral = await collection.find_one({"custom_param": param})
            
            if referral and '_id' in referral:
                del referral['_id']
            
            return referral
            
        except Exception as e:
            logger.error(f"Error getting custom referral by param: {e}")
            return None
    
    async def get_custom_referrals_by_user(self, user_id: int) -> List[Dict[str, Any]]:
        """Get custom referrals for a specific user"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            cursor = collection.find({"user_id": user_id})
            referrals = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for referral in referrals:
                if '_id' in referral:
                    del referral['_id']
            
            return referrals
            
        except Exception as e:
            logger.error(f"Error getting custom referrals by user: {e}")
            return []
    
    async def create_custom_referral(self, custom_param: str, user_id: int, created_by: int) -> Dict[str, Any]:
        """Create custom referral link (matching PHP logic exactly)"""
        try:
            # Validate custom parameter
            validation = self.validate_custom_param(custom_param)
            if not validation['valid']:
                return {
                    'success': False,
                    'message': validation['message']
                }
            
            # Check if parameter already exists
            existing = await self.get_custom_referral_by_param(custom_param)
            if existing:
                return {
                    'success': False,
                    'message': f'Custom parameter "{custom_param}" already exists.'
                }
            
            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            target_user = await user_service.get_user(user_id)
            
            if not target_user:
                return {
                    'success': False,
                    'message': f'User with ID {user_id} not found.'
                }
            
            # Create custom referral data
            referral_data = {
                'custom_param': custom_param,
                'user_id': user_id,
                'user_name': target_user.get('first_name', 'Unknown'),
                'username': target_user.get('username', ''),
                'created_by': created_by,
                'created_at': get_current_timestamp(),
                'updated_at': get_current_timestamp(),
                'clicks': 0,
                'referrals': 0,
                'active': True
            }
            
            # Save to database
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            result = await collection.insert_one(referral_data)
            
            if result.inserted_id:
                # Generate the custom link
                from config.settings import settings
                custom_link = f"https://t.me/{settings.BOT_USERNAME}?start={custom_param}"
                
                return {
                    'success': True,
                    'message': 'Custom referral link created successfully!',
                    'custom_param': custom_param,
                    'custom_link': custom_link,
                    'user_name': target_user.get('first_name', 'Unknown'),
                    'user_id': user_id
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to create custom referral link.'
                }
            
        except Exception as e:
            logger.error(f"Error creating custom referral: {e}")
            return {
                'success': False,
                'message': 'An error occurred while creating custom referral link.'
            }
    
    async def update_custom_referral(self, old_param: str, new_param: str, admin_id: int) -> Dict[str, Any]:
        """Update custom referral parameter (matching PHP logic exactly)"""
        try:
            # Validate new parameter
            validation = self.validate_custom_param(new_param)
            if not validation['valid']:
                return {
                    'success': False,
                    'message': validation['message']
                }
            
            # Check if old parameter exists
            existing = await self.get_custom_referral_by_param(old_param)
            if not existing:
                return {
                    'success': False,
                    'message': f'Custom parameter "{old_param}" not found.'
                }
            
            # Check if new parameter already exists (and is different from old)
            if old_param != new_param:
                new_existing = await self.get_custom_referral_by_param(new_param)
                if new_existing:
                    return {
                        'success': False,
                        'message': f'Custom parameter "{new_param}" already exists.'
                    }
            
            # Update the parameter
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            result = await collection.update_one(
                {"custom_param": old_param},
                {
                    "$set": {
                        "custom_param": new_param,
                        "updated_at": get_current_timestamp(),
                        "updated_by": admin_id
                    }
                }
            )
            
            if result.modified_count > 0:
                # Generate the new custom link
                from config.settings import settings
                custom_link = f"https://t.me/{settings.BOT_USERNAME}?start={new_param}"
                
                return {
                    'success': True,
                    'message': 'Custom referral parameter updated successfully!',
                    'old_param': old_param,
                    'new_param': new_param,
                    'custom_link': custom_link,
                    'user_name': existing.get('user_name', 'Unknown'),
                    'user_id': existing.get('user_id')
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to update custom referral parameter.'
                }
            
        except Exception as e:
            logger.error(f"Error updating custom referral: {e}")
            return {
                'success': False,
                'message': 'An error occurred while updating custom referral.'
            }
    
    async def delete_custom_referral(self, param: str, admin_id: int) -> Dict[str, Any]:
        """Delete custom referral link (matching PHP logic exactly)"""
        try:
            # Check if parameter exists
            existing = await self.get_custom_referral_by_param(param)
            if not existing:
                return {
                    'success': False,
                    'message': f'Custom parameter "{param}" not found.'
                }
            
            # Delete the referral
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            result = await collection.delete_one({"custom_param": param})
            
            if result.deleted_count > 0:
                return {
                    'success': True,
                    'message': 'Custom referral link deleted successfully!',
                    'param': param,
                    'user_name': existing.get('user_name', 'Unknown'),
                    'user_id': existing.get('user_id')
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to delete custom referral link.'
                }
            
        except Exception as e:
            logger.error(f"Error deleting custom referral: {e}")
            return {
                'success': False,
                'message': 'An error occurred while deleting custom referral.'
            }
    
    async def track_custom_referral_click(self, param: str) -> bool:
        """Track custom referral click"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            result = await collection.update_one(
                {"custom_param": param},
                {
                    "$inc": {"clicks": 1},
                    "$set": {"last_clicked": get_current_timestamp()}
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error tracking custom referral click: {e}")
            return False
    
    async def track_custom_referral_conversion(self, param: str) -> bool:
        """Track custom referral conversion (new user joined)"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            result = await collection.update_one(
                {"custom_param": param},
                {
                    "$inc": {"referrals": 1},
                    "$set": {"last_referral": get_current_timestamp()}
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error tracking custom referral conversion: {e}")
            return False
    
    def validate_custom_param(self, param: str) -> Dict[str, Any]:
        """Validate custom parameter format (matching PHP logic exactly)"""
        try:
            # Check length
            if len(param) < 3:
                return {
                    'valid': False,
                    'message': 'Custom parameter must be at least 3 characters long.'
                }
            
            if len(param) > 50:
                return {
                    'valid': False,
                    'message': 'Custom parameter must be no more than 50 characters long.'
                }
            
            # Check format - only letters, numbers, hyphens, and underscores
            if not re.match(r'^[a-zA-Z0-9_-]+$', param):
                return {
                    'valid': False,
                    'message': 'Custom parameter can only contain letters, numbers, hyphens, and underscores.'
                }
            
            # Check for reserved words
            reserved_words = [
                'start', 'help', 'admin', 'bot', 'api', 'www', 'http', 'https',
                'telegram', 'channel', 'group', 'user', 'referral', 'ref'
            ]
            
            if param.lower() in reserved_words:
                return {
                    'valid': False,
                    'message': f'"{param}" is a reserved word and cannot be used.'
                }
            
            return {
                'valid': True,
                'message': 'Valid custom parameter.'
            }
            
        except Exception as e:
            logger.error(f"Error validating custom param: {e}")
            return {
                'valid': False,
                'message': 'Error validating custom parameter.'
            }

    # ==================== NEW ADMIN MANAGEMENT METHODS ====================

    async def get_custom_referral_system_status(self) -> bool:
        """Get current custom referral system status"""
        try:
            admin_settings_collection = await get_collection(COLLECTIONS['admin_settings'])

            # Get custom referral settings
            referral_settings = await admin_settings_collection.find_one({"setting_type": "custom_referral"})

            if not referral_settings:
                # Create default settings (enabled by default)
                default_settings = {
                    "setting_type": "custom_referral",
                    "enabled": True,
                    "created_at": get_current_timestamp(),
                    "updated_at": get_current_timestamp()
                }
                await admin_settings_collection.insert_one(default_settings)
                return True

            return referral_settings.get("enabled", True)

        except Exception as e:
            logger.error(f"Error getting custom referral system status: {e}")
            return True  # Default to enabled

    async def toggle_custom_referral_system(self, admin_user_id: int) -> tuple:
        """Toggle custom referral system status"""
        try:
            admin_settings_collection = await get_collection(COLLECTIONS['admin_settings'])

            # Get current status
            current_status = await self.get_custom_referral_system_status()
            new_status = not current_status

            # Update status
            await admin_settings_collection.update_one(
                {"setting_type": "custom_referral"},
                {
                    "$set": {
                        "enabled": new_status,
                        "updated_at": get_current_timestamp(),
                        "updated_by": admin_user_id
                    }
                },
                upsert=True
            )

            status_text = "ENABLED" if new_status else "DISABLED"
            return True, f"✅ Custom referral system {status_text}"

        except Exception as e:
            logger.error(f"Error toggling custom referral system: {e}")
            return False, "❌ Failed to toggle custom referral system"

    async def get_user_custom_link(self, user_id: int) -> str:
        """Get user's custom referral link if it exists"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])

            # Find custom referral for this user
            custom_referral = await collection.find_one({"user_id": user_id})

            if custom_referral:
                # Generate the custom link
                from config.settings import settings
                custom_param = custom_referral.get('custom_param')
                if custom_param:
                    return f"https://t.me/{settings.BOT_USERNAME}?start={custom_param}"

            return None

        except Exception as e:
            logger.error(f"Error getting user custom link: {e}")
            return None

    async def user_has_custom_link(self, user_id: int) -> bool:
        """Check if user already has a custom referral link"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])

            # Check if user has a custom referral
            custom_referral = await collection.find_one({"user_id": user_id})

            return custom_referral is not None

        except Exception as e:
            logger.error(f"Error checking user custom link: {e}")
            return False

    async def get_admin_statistics(self) -> Dict[str, Any]:
        """Get custom referral statistics for admin panel"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])

            # Get total count
            total_links = await collection.count_documents({})

            # Get recent links (last 5)
            recent_links = await collection.find({}).sort("created_at", -1).limit(5).to_list(length=5)

            # Format recent links for display
            formatted_recent = []
            for link in recent_links:
                user_info = await self._get_user_info(link.get('user_id'))
                formatted_recent.append({
                    'parameter': link.get('custom_param'),
                    'username': user_info.get('username', 'Unknown'),
                    'user_id': link.get('user_id'),
                    'created_at': link.get('created_at')
                })

            return {
                'total_links': total_links,
                'recent_links': formatted_recent,
                'system_enabled': await self.get_custom_referral_system_status()
            }

        except Exception as e:
            logger.error(f"Error getting admin statistics: {e}")
            return {
                'total_links': 0,
                'recent_links': [],
                'system_enabled': True
            }

    async def _get_user_info(self, user_id: int) -> Dict[str, Any]:
        """Get user information by user ID"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            user = await users_collection.find_one({"user_id": user_id})

            if user:
                return {
                    'username': user.get('username', 'Unknown'),
                    'first_name': user.get('first_name', 'Unknown')
                }
            else:
                return {'username': 'Unknown', 'first_name': 'Unknown'}

        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return {'username': 'Unknown', 'first_name': 'Unknown'}

    async def delete_custom_link_by_parameter(self, parameter: str, admin_user_id: int) -> tuple:
        """Delete custom referral link by parameter"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])

            # Find the link
            link_data = await collection.find_one({"custom_param": parameter})

            if not link_data:
                return False, f"❌ No custom link found with parameter: {parameter}"

            # Delete the link
            result = await collection.delete_one({"custom_param": parameter})

            if result.deleted_count > 0:
                user_id = link_data.get('user_id')
                user_info = await self._get_user_info(user_id)

                return True, f"✅ Custom link deleted successfully!\n\nParameter: {parameter}\nUser: {user_info['username']} ({user_id})"
            else:
                return False, "❌ Failed to delete custom link"

        except Exception as e:
            logger.error(f"Error deleting custom link by parameter: {e}")
            return False, "❌ Something went wrong while deleting custom link"

    async def delete_custom_link_by_user_id(self, user_id: int, admin_user_id: int) -> tuple:
        """Delete custom referral link by user ID"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])

            # Find the link
            link_data = await collection.find_one({"user_id": user_id})

            if not link_data:
                return False, f"❌ No custom link found for user ID: {user_id}"

            # Delete the link
            result = await collection.delete_one({"user_id": user_id})

            if result.deleted_count > 0:
                parameter = link_data.get('custom_param')
                user_info = await self._get_user_info(user_id)

                return True, f"✅ Custom link deleted successfully!\n\nParameter: {parameter}\nUser: {user_info['username']} ({user_id})"
            else:
                return False, "❌ Failed to delete custom link"

        except Exception as e:
            logger.error(f"Error deleting custom link by user ID: {e}")
            return False, "❌ Something went wrong while deleting custom link"

    def get_system_disabled_message(self) -> str:
        """Get message when custom referral system is disabled"""
        return (
            "🚫 Custom invite link feature is currently not available\n\n"
            "Please try again later or contact support for more info\n"
            "@instantohelpbot"
        )

    def get_user_already_has_link_message(self, custom_link: str) -> str:
        """Get message when user already has a custom link"""
        return (
            "🚫 You have already created your custom invite link and it cannot be changed.\n\n"
            f"🔗 Your Link: {custom_link}\n\n"
            "Each user is allowed only one custom invite link. Please continue sharing this link to earn rewards!"
        )

    async def format_admin_panel_message(self) -> str:
        """Format custom referral status message for admin panel"""
        try:
            stats = await self.get_admin_statistics()

            message = "⚙️ Hello, Welcome To Custom Referral Link Setup!\n\n"
            message += "🔗 Custom Referral Link Management\n\n"
            message += f"📊 Total Custom Links: {stats['total_links']}\n\n"

            if stats['recent_links']:
                message += "📋 Recent Links:\n"
                for link in stats['recent_links']:
                    message += f"• {link['parameter']} → {link['username']} ({link['user_id']})\n"

                if stats['total_links'] > 5:
                    remaining = stats['total_links'] - 5
                    message += f"• ... and {remaining} more\n"
            else:
                message += "📋 Recent Links:\nNo custom links created yet.\n"

            message += "\n📝 Quick Commands:\n"
            message += "• /customref list - View all links\n"
            message += "• /customref create [param] [user_id] - Create link\n"
            message += "• /customref help - Full command list\n\n"
            message += "💡 Use commands in chat for detailed management\n\n"

            status_text = "ENABLED" if stats['system_enabled'] else "DISABLED"
            message += f"Current Status: {status_text}"

            return message

        except Exception as e:
            logger.error(f"Error formatting admin panel message: {e}")
            return "⚙️ Hello, Welcome To Custom Referral Link Setup!\n\nError loading custom referral status."
