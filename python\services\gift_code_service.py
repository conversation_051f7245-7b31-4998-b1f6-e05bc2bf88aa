"""
Gift Code service for managing gift code generation and redemption
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.gift_code import GiftCodeModel
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class GiftCodeService:
    """Service for gift code operations"""
    
    def __init__(self):
        pass
    
    async def get_all_gift_codes(self) -> List[Dict[str, Any]]:
        """Get all gift codes (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            cursor = collection.find({})
            gift_codes = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for code in gift_codes:
                if '_id' in code:
                    del code['_id']
            
            return gift_codes
            
        except Exception as e:
            logger.error(f"Error getting all gift codes: {e}")
            return []
    
    async def add_gift_code(self, code_data: Dict[str, Any]) -> bool:
        """Add gift code to database (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            # Check if code already exists (case-insensitive)
            existing_code = await collection.find_one({
                "code": {"$regex": f"^{code_data['code']}$", "$options": "i"}
            })
            
            if existing_code:
                return False  # Code already exists
            
            # Add timestamps and initial values
            code_data['created_at'] = get_current_timestamp()
            code_data['used_count'] = 0
            code_data['last_used'] = 0
            code_data['redeemed_by'] = []
            
            result = await collection.insert_one(code_data)
            
            return result.inserted_id is not None
            
        except Exception as e:
            logger.error(f"Error adding gift code: {e}")
            return False
    
    async def redeem_gift_code(self, code: str, user_id: int) -> Dict[str, Any]:
        """Redeem gift code for user (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            # Find gift code (case-insensitive)
            gift_code = await collection.find_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })

            if not gift_code:
                return {
                    'success': False,
                    'message': 'Invalid gift code.'
                }


            
            # Check redemption eligibility
            eligibility = GiftCodeModel.check_code_redemption_eligibility(gift_code, user_id)
            
            if not eligibility['eligible']:
                return {
                    'success': False, 
                    'message': eligibility['message']
                }
            
            # Update gift code usage (handle both traditional and admin gift codes)
            update_data = {
                "$set": {
                    "last_used": get_current_timestamp()
                }
            }

            # Handle traditional gift codes
            if 'redeemed_by' in gift_code:
                redeemed_by = gift_code.get('redeemed_by', [])
                redeemed_by.append(user_id)
                update_data["$inc"] = {"used_count": 1}
                update_data["$set"]["redeemed_by"] = redeemed_by
            # Handle admin gift codes
            elif 'users_claimed' in gift_code:
                update_data["$inc"] = {"usage_count": 1}
                update_data["$push"] = {"users_claimed": user_id}
            else:
                # Fallback for unknown structure
                update_data["$inc"] = {"used_count": 1}
                update_data["$push"] = {"redeemed_by": user_id}

            update_result = await collection.update_one(
                {"code": {"$regex": f"^{code}$", "$options": "i"}},
                update_data
            )
            
            if update_result.modified_count == 0:
                return {
                    'success': False, 
                    'message': 'Error processing gift code redemption.'
                }
            
            # Update user balance
            from services.user_service import UserService
            user_service = UserService()

            # Safely extract amount with validation - handle both traditional and admin gift codes
            amount = None

            # Check for traditional gift code with fixed amount
            if 'amount' in gift_code:
                amount = gift_code.get('amount')
            # Check for admin gift code with min/max amount range
            elif 'min_amount' in gift_code and 'max_amount' in gift_code:
                min_amount = gift_code.get('min_amount', 0)
                max_amount = gift_code.get('max_amount', 0)

                # Generate random amount within range for admin gift codes
                import random
                amount = round(random.uniform(float(min_amount), float(max_amount)), 2)

            if amount is None:
                logger.error(f"Gift code missing amount fields: {gift_code}")
                return {
                    'success': False,
                    'message': 'Invalid gift code data structure.'
                }

            try:
                amount = float(amount)
            except (ValueError, TypeError) as e:
                logger.error(f"Invalid amount value in gift code: {amount}, error: {e}")
                return {
                    'success': False,
                    'message': 'Invalid gift code amount.'
                }

            balance_updated = await user_service.update_user_balance(user_id, int(amount), 'add')
            
            if not balance_updated:
                # Rollback gift code update (handle both traditional and admin gift codes)
                rollback_data = {}

                # Handle traditional gift codes
                if 'redeemed_by' in gift_code:
                    rollback_data = {
                        "$inc": {"used_count": -1},
                        "$set": {
                            "redeemed_by": gift_code.get('redeemed_by', [])
                        }
                    }
                # Handle admin gift codes
                elif 'users_claimed' in gift_code:
                    rollback_data = {
                        "$inc": {"usage_count": -1},
                        "$pull": {"users_claimed": user_id}
                    }
                else:
                    # Fallback for unknown structure
                    rollback_data = {
                        "$inc": {"used_count": -1},
                        "$pull": {"redeemed_by": user_id}
                    }

                await collection.update_one(
                    {"code": {"$regex": f"^{code}$", "$options": "i"}},
                    rollback_data
                )

                return {
                    'success': False,
                    'message': 'Error updating user balance.'
                }
            
            return {
                'success': True, 
                'amount': amount
            }
            
        except Exception as e:
            logger.error(f"Error redeeming gift code: {e}")
            return {
                'success': False, 
                'message': 'Error processing gift code redemption.'
            }
    
    async def check_code_exists(self, code: str) -> bool:
        """Check if gift code already exists (case-insensitive)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            existing_code = await collection.find_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            return existing_code is not None
            
        except Exception as e:
            logger.error(f"Error checking code existence: {e}")
            return False
    
    async def get_gift_code_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """Get gift code by code string"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            gift_code = await collection.find_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            if gift_code and '_id' in gift_code:
                del gift_code['_id']
            
            return gift_code
            
        except Exception as e:
            logger.error(f"Error getting gift code by code: {e}")
            return None
    
    async def get_gift_codes_by_admin(self, admin_id: int) -> List[Dict[str, Any]]:
        """Get gift codes created by specific admin"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            cursor = collection.find({"created_by": admin_id})
            gift_codes = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for code in gift_codes:
                if '_id' in code:
                    del code['_id']
            
            return gift_codes
            
        except Exception as e:
            logger.error(f"Error getting gift codes by admin: {e}")
            return []
    
    async def get_active_gift_codes(self) -> List[Dict[str, Any]]:
        """Get only active (non-expired, non-exhausted) gift codes"""
        try:
            all_codes = await self.get_all_gift_codes()
            active_codes = []
            current_time = get_current_timestamp()
            
            for code in all_codes:
                # Check if expired
                if (code.get('expiry_date', 0) > 0 and 
                    current_time > code['expiry_date']):
                    continue
                
                # Check if exhausted
                usage_limit = code.get('usage_limit', 0)
                used_count = code.get('used_count', 0)
                if usage_limit > 0 and used_count >= usage_limit:
                    continue
                
                active_codes.append(code)
            
            return active_codes
            
        except Exception as e:
            logger.error(f"Error getting active gift codes: {e}")
            return []
    
    async def delete_gift_code(self, code: str) -> bool:
        """Delete gift code (admin function)"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            result = await collection.delete_one({
                "code": {"$regex": f"^{code}$", "$options": "i"}
            })
            
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting gift code: {e}")
            return False
    
    async def update_gift_code_expiry(self, code: str, expiry_timestamp: int) -> bool:
        """Update gift code expiry date"""
        try:
            collection = await get_collection(COLLECTIONS['gift_codes'])
            
            result = await collection.update_one(
                {"code": {"$regex": f"^{code}$", "$options": "i"}},
                {"$set": {"expiry_date": expiry_timestamp}}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating gift code expiry: {e}")
            return False
    
    async def get_gift_code_statistics(self) -> Dict[str, Any]:
        """Get comprehensive gift code statistics"""
        try:
            all_codes = await self.get_all_gift_codes()
            return GiftCodeModel.get_gift_code_statistics(all_codes)
            
        except Exception as e:
            logger.error(f"Error getting gift code statistics: {e}")
            return {
                'total_codes': 0,
                'active_codes': 0,
                'expired_codes': 0,
                'exhausted_codes': 0,
                'total_amount': 0,
                'total_redeemed': 0,
                'total_redemptions': 0
            }
    
    async def generate_admin_gift_codes_message(self) -> str:
        """Generate admin gift codes management message"""
        try:
            gift_codes = await self.get_all_gift_codes()
            return GiftCodeModel.format_admin_gift_codes_list_message(gift_codes)
            
        except Exception as e:
            logger.error(f"Error generating admin gift codes message: {e}")
            return "🎫 <b>Gift Codes Management</b>\n\n❌ Error loading gift codes. Please try again later."
    
    async def generate_admin_gift_codes_keyboard(self):
        """Generate admin gift codes management keyboard"""
        try:
            return GiftCodeModel.create_admin_gift_codes_keyboard()
            
        except Exception as e:
            logger.error(f"Error generating admin gift codes keyboard: {e}")
            # Return basic keyboard if there's an error
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

    # ==================== NEW ADMIN GIFT CODE MANAGEMENT ====================

    async def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """Get comprehensive gift code statistics for admin panel"""
        try:
            gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])
            link_codes_collection = await get_collection(COLLECTIONS['link_based_codes'])

            # Get all gift codes to analyze properly
            all_codes = await gift_codes_collection.find({}).to_list(length=None)

            total_codes = len(all_codes)
            live_codes = 0
            used_codes = 0
            total_claims = 0
            total_amount = 0

            current_time = get_current_timestamp()

            for code in all_codes:
                # Handle both traditional and admin gift codes
                if 'is_active' in code:
                    # Admin gift code (new system)
                    usage_count = code.get('usage_count', 0)
                    usage_limit = code.get('usage_limit', 0)

                    # Check if code is exhausted or expired
                    is_exhausted = usage_limit > 0 and usage_count >= usage_limit
                    is_expired = code.get('expiry_date', 0) > 0 and current_time > code.get('expiry_date', 0)

                    if code.get('is_active', True) and not is_exhausted and not is_expired:
                        live_codes += 1
                    else:
                        used_codes += 1

                    # Count claims and amounts for admin codes
                    total_claims += usage_count
                    # For admin codes, calculate total distributed amount
                    if usage_count > 0:
                        min_amount = code.get('min_amount', 0)
                        max_amount = code.get('max_amount', 0)
                        avg_amount = (min_amount + max_amount) / 2
                        total_amount += avg_amount * usage_count

                else:
                    # Traditional gift code (PHP migration)
                    used_count = code.get('used_count', 0)
                    usage_limit = code.get('usage_limit', 0)

                    # Check if code is exhausted or expired
                    is_exhausted = usage_limit > 0 and used_count >= usage_limit
                    is_expired = code.get('expiry_date', 0) > 0 and current_time > code.get('expiry_date', 0)

                    if not is_exhausted and not is_expired:
                        live_codes += 1
                    else:
                        used_codes += 1

                    # Count claims and amounts for traditional codes
                    total_claims += used_count
                    amount = code.get('amount', 0)
                    total_amount += amount * used_count

            # Link-based code stats
            total_link_codes = await link_codes_collection.count_documents({})

            link_claims_pipeline = [
                {"$group": {
                    "_id": None,
                    "total_link_claims": {"$sum": "$usage_count"},
                    "total_link_amount": {"$sum": "$total_distributed"}
                }}
            ]

            link_result = await link_codes_collection.aggregate(link_claims_pipeline).to_list(length=1)
            total_link_claims = link_result[0].get('total_link_claims', 0) if link_result else 0
            total_link_amount = link_result[0].get('total_link_amount', 0) if link_result else 0

            return {
                'total_codes': total_codes,
                'live_codes': live_codes,
                'used_codes': used_codes,
                'total_claims': total_claims,
                'total_amount': total_amount,
                'total_link_codes': total_link_codes,
                'total_link_claims': total_link_claims,
                'total_link_amount': total_link_amount
            }

        except Exception as e:
            logger.error(f"Error getting comprehensive statistics: {e}")
            return {}

    async def create_admin_gift_code(self, code: str, min_amount: float, max_amount: float, usage_limit: int, created_by: int) -> bool:
        """Create a new traditional gift code from admin panel"""
        try:
            gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])

            # Check if code already exists
            existing_code = await gift_codes_collection.find_one({"code": code})
            if existing_code:
                return False

            gift_code_data = {
                "code": code,
                "min_amount": min_amount,
                "max_amount": max_amount,
                "usage_limit": usage_limit,
                "usage_count": 0,
                "is_active": True,
                "created_by": created_by,
                "created_at": get_current_timestamp(),
                "users_claimed": []
            }

            result = await gift_codes_collection.insert_one(gift_code_data)
            return result.inserted_id is not None

        except Exception as e:
            logger.error(f"Error creating admin gift code: {e}")
            return False

    async def get_live_codes_detailed(self) -> List[Dict[str, Any]]:
        """Get all active gift codes with detailed information"""
        try:
            gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])
            current_time = get_current_timestamp()

            # Get all codes and filter for truly active ones
            all_codes = await gift_codes_collection.find({}).sort("created_at", -1).to_list(length=None)
            live_codes = []

            for code in all_codes:
                is_live = False

                if 'is_active' in code:
                    # Admin gift code (new system)
                    usage_count = code.get('usage_count', 0)
                    usage_limit = code.get('usage_limit', 0)

                    # Check if code is truly active (not exhausted, not expired, and is_active=True)
                    is_exhausted = usage_limit > 0 and usage_count >= usage_limit
                    is_expired = code.get('expiry_date', 0) > 0 and current_time > code.get('expiry_date', 0)
                    is_active = code.get('is_active', True)

                    if is_active and not is_exhausted and not is_expired:
                        is_live = True

                else:
                    # Traditional gift code (PHP migration)
                    used_count = code.get('used_count', 0)
                    usage_limit = code.get('usage_limit', 0)

                    # Check if code is truly active (not exhausted, not expired)
                    is_exhausted = usage_limit > 0 and used_count >= usage_limit
                    is_expired = code.get('expiry_date', 0) > 0 and current_time > code.get('expiry_date', 0)

                    if not is_exhausted and not is_expired:
                        is_live = True

                if is_live:
                    live_codes.append(code)

            return live_codes

        except Exception as e:
            logger.error(f"Error getting live codes detailed: {e}")
            return []

    async def get_used_codes_detailed(self) -> List[Dict[str, Any]]:
        """Get all completed/expired gift codes with detailed information"""
        try:
            gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])

            # Get all codes and filter for used/completed ones
            all_codes = await gift_codes_collection.find({}).sort("created_at", -1).to_list(length=None)
            used_codes = []
            current_time = get_current_timestamp()

            for code in all_codes:
                is_used = False

                if 'is_active' in code:
                    # Admin gift code (new system)
                    usage_count = code.get('usage_count', 0)
                    usage_limit = code.get('usage_limit', 0)

                    # Check if code is exhausted, expired, or deactivated
                    is_exhausted = usage_limit > 0 and usage_count >= usage_limit
                    is_expired = code.get('expiry_date', 0) > 0 and current_time > code.get('expiry_date', 0)
                    is_deactivated = not code.get('is_active', True)

                    if is_exhausted or is_expired or is_deactivated or usage_count > 0:
                        is_used = True

                else:
                    # Traditional gift code (PHP migration)
                    used_count = code.get('used_count', 0)
                    usage_limit = code.get('usage_limit', 0)

                    # Check if code is exhausted, expired, or has been used
                    is_exhausted = usage_limit > 0 and used_count >= usage_limit
                    is_expired = code.get('expiry_date', 0) > 0 and current_time > code.get('expiry_date', 0)

                    if is_exhausted or is_expired or used_count > 0:
                        is_used = True

                if is_used:
                    used_codes.append(code)

            return used_codes

        except Exception as e:
            logger.error(f"Error getting used codes detailed: {e}")
            return []

    async def generate_link_code(self) -> str:
        """Generate unique link-based code"""
        try:
            import random
            import string

            link_codes_collection = await get_collection(COLLECTIONS['link_based_codes'])

            # Generate unique 8-character alphanumeric code
            while True:
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
                existing = await link_codes_collection.find_one({"code": code})
                if not existing:
                    return code

        except Exception as e:
            logger.error(f"Error generating link code: {e}")
            return None

    async def create_link_based_code(
        self,
        code: str,
        amount_type: str,
        amount_value: Any,
        user_limit: int,
        created_by: int
    ) -> bool:
        """Create a new link-based redemption code"""
        try:
            link_codes_collection = await get_collection(COLLECTIONS['link_based_codes'])

            link_code_data = {
                "code": code,
                "amount_type": amount_type,  # 'fixed' or 'random'
                "amount_value": amount_value,  # float for fixed, dict with min/max for random
                "user_limit": user_limit,  # -1 for unlimited
                "usage_count": 0,
                "total_distributed": 0,
                "is_active": True,
                "created_by": created_by,
                "created_at": get_current_timestamp(),
                "users_claimed": []
            }

            result = await link_codes_collection.insert_one(link_code_data)
            return result.inserted_id is not None

        except Exception as e:
            logger.error(f"Error creating link-based code: {e}")
            return False

    async def redeem_link_code(self, code: str, user_id: int) -> tuple:
        """Redeem a link-based code"""
        try:
            import random

            link_codes_collection = await get_collection(COLLECTIONS['link_based_codes'])

            # Find the code
            link_code = await link_codes_collection.find_one({"code": code, "is_active": True})

            if not link_code:
                return False, "<b>🚫 This redeem link is no longer valid or has expired.</b>", 0

            # Check if user already claimed
            if user_id in link_code.get('users_claimed', []):
                return False, "⚠️ You've already used this redeem link.", 0

            # Check usage limit
            if link_code['user_limit'] != -1 and link_code['usage_count'] >= link_code['user_limit']:
                # Deactivate code
                await link_codes_collection.update_one(
                    {"code": code},
                    {"$set": {"is_active": False}}
                )
                return False, "😔 This redeem link has reached its usage limit.", 0

            # Calculate amount based on type
            if link_code['amount_type'] == 'fixed':
                amount = float(link_code['amount_value'])
            else:  # random
                min_amount = link_code['amount_value']['min']
                max_amount = link_code['amount_value']['max']
                amount = round(random.uniform(min_amount, max_amount), 2)

            # Update code usage
            await link_codes_collection.update_one(
                {"code": code},
                {
                    "$inc": {"usage_count": 1, "total_distributed": amount},
                    "$push": {"users_claimed": user_id}
                }
            )

            # Check if code should be deactivated
            if link_code['user_limit'] != -1 and link_code['usage_count'] + 1 >= link_code['user_limit']:
                await link_codes_collection.update_one(
                    {"code": code},
                    {"$set": {"is_active": False}}
                )

            return True, f"You've received ₹{amount} from a redeem link!", amount

        except Exception as e:
            logger.error(f"Error redeeming link code: {e}")
            return False, "Something went wrong", 0
