"""
Level Rewards service for managing referral level system
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.level_rewards import LevelRewardsModel
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class LevelRewardsService:
    """Service for level rewards operations"""
    
    def __init__(self):
        pass
    
    async def get_level_rewards_config(self) -> Dict[str, Any]:
        """Get level rewards configuration (matching PHP logic exactly)"""
        try:
            # Use global admin ID (0) for level rewards configuration to ensure consistency
            global_admin_id = 0
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await collection.find_one({"admin_id": global_admin_id})
            
            if admin_settings and 'level_rewards_config' in admin_settings:
                config = admin_settings['level_rewards_config']
                
                # Validate configuration structure
                validation = LevelRewardsModel.validate_config(config)
                if validation['valid']:
                    return config
            
            # Return default configuration if none found or invalid
            return LevelRewardsModel.get_default_config()
            
        except Exception as e:
            logger.error(f"Error getting level rewards config: {e}")
            return LevelRewardsModel.get_default_config()
    
    async def is_level_rewards_enabled(self) -> bool:
        """Check if level rewards system is enabled (matching PHP logic exactly)"""
        try:
            # Use global admin ID (0) for level rewards configuration
            global_admin_id = 0
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await collection.find_one({"admin_id": global_admin_id})
            
            if admin_settings and 'level_rewards_enabled' in admin_settings:
                return bool(admin_settings['level_rewards_enabled'])
            
            # Default to enabled if not found
            return True
            
        except Exception as e:
            logger.error(f"Error checking level rewards enabled status: {e}")
            return True
    
    async def update_level_rewards_config(
        self, 
        referral_requirements: List[int], 
        bonus_amounts: List[float]
    ) -> bool:
        """Update level rewards configuration (matching PHP logic exactly)"""
        try:
            # Validate input arrays
            if (not isinstance(referral_requirements, list) or 
                not isinstance(bonus_amounts, list) or
                len(referral_requirements) != 6 or 
                len(bonus_amounts) != 6):
                return False
            
            # Ensure all values are numeric and positive
            for req in referral_requirements:
                if not isinstance(req, (int, float)) or req < 0:
                    return False
            
            for amount in bonus_amounts:
                if not isinstance(amount, (int, float)) or amount < 0:
                    return False
            
            # Create configuration
            config = {
                'referral_requirements': [int(req) for req in referral_requirements],
                'bonus_amounts': [float(amount) for amount in bonus_amounts]
            }
            
            # Update configuration using global admin ID
            global_admin_id = 0
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            
            result = await collection.update_one(
                {"admin_id": global_admin_id},
                {
                    "$set": {
                        "level_rewards_config": config,
                        "updated_at": get_current_timestamp()
                    }
                },
                upsert=True
            )
            
            return result.modified_count > 0 or result.upserted_id is not None
            
        except Exception as e:
            logger.error(f"Error updating level rewards config: {e}")
            return False
    
    async def toggle_level_rewards(self, enabled: bool) -> bool:
        """Toggle level rewards system (matching PHP logic exactly)"""
        try:
            # Use global admin ID (0) for level rewards configuration
            global_admin_id = 0
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            
            result = await collection.update_one(
                {"admin_id": global_admin_id},
                {
                    "$set": {
                        "level_rewards_enabled": enabled,
                        "updated_at": get_current_timestamp()
                    }
                },
                upsert=True
            )
            
            return result.modified_count > 0 or result.upserted_id is not None
            
        except Exception as e:
            logger.error(f"Error toggling level rewards: {e}")
            return False
    
    async def get_user_referral_count(self, user_id: int) -> int:
        """Get user's referral count (matching PHP logic exactly)"""
        try:
            from services.user_service import UserService
            user_service = UserService()
            
            user = await user_service.get_user(user_id)
            if not user:
                return 0
            
            # Count referrals from promotion report
            promotion_report = user.get('promotion_report', [])
            return len(promotion_report)
            
        except Exception as e:
            logger.error(f"Error getting user referral count: {e}")
            return 0
    
    async def get_user_claimed_levels(self, user_id: int) -> List[int]:
        """Get user's claimed levels (matching PHP logic exactly)"""
        try:
            from services.user_service import UserService
            user_service = UserService()
            
            user = await user_service.get_user(user_id)
            if not user:
                return []
            
            return user.get('claimed_levels', [])
            
        except Exception as e:
            logger.error(f"Error getting user claimed levels: {e}")
            return []
    
    async def get_user_current_level(self, user_id: int) -> int:
        """Get user's current level (matching PHP logic exactly)"""
        try:
            referral_count = await self.get_user_referral_count(user_id)
            config = await self.get_level_rewards_config()
            
            return LevelRewardsModel.calculate_user_current_level(referral_count, config)
            
        except Exception as e:
            logger.error(f"Error getting user current level: {e}")
            return 0
    
    async def get_user_eligible_levels(self, user_id: int) -> List[int]:
        """Get levels user is eligible to claim (matching PHP logic exactly)"""
        try:
            referral_count = await self.get_user_referral_count(user_id)
            claimed_levels = await self.get_user_claimed_levels(user_id)
            config = await self.get_level_rewards_config()
            
            return LevelRewardsModel.get_user_eligible_levels(
                referral_count, claimed_levels, config
            )
            
        except Exception as e:
            logger.error(f"Error getting user eligible levels: {e}")
            return []
    
    async def get_next_level_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get next level information (matching PHP logic exactly)"""
        try:
            referral_count = await self.get_user_referral_count(user_id)
            current_level = await self.get_user_current_level(user_id)
            config = await self.get_level_rewards_config()
            
            return LevelRewardsModel.get_next_level_info(
                referral_count, current_level, config
            )
            
        except Exception as e:
            logger.error(f"Error getting next level info: {e}")
            return None
    
    async def claim_level_bonus(self, user_id: int, level: int) -> Dict[str, Any]:
        """Claim level bonus for user (matching PHP logic exactly)"""
        try:
            config = await self.get_level_rewards_config()
            eligible_levels = await self.get_user_eligible_levels(user_id)
            
            if level not in eligible_levels:
                return {
                    'success': False, 
                    'message': 'You are not eligible for this level bonus.'
                }
            
            bonus_amount = float(config['bonus_amounts'][level - 1])
            
            # Add bonus to user balance and mark level as claimed
            from services.user_service import UserService
            user_service = UserService()
            
            # Update user balance
            if await user_service.update_user_balance(user_id, bonus_amount, 'add'):
                # Mark level as claimed
                if await self.update_user_claimed_levels(user_id, level):
                    return {
                        'success': True,
                        'amount': bonus_amount,
                        'level': level
                    }
                else:
                    # Rollback balance if we can't mark as claimed
                    await user_service.update_user_balance(user_id, bonus_amount, 'subtract')
            
            return {
                'success': False, 
                'message': 'Error processing level bonus claim.'
            }
            
        except Exception as e:
            logger.error(f"Error claiming level bonus: {e}")
            return {
                'success': False, 
                'message': 'Error processing level bonus claim.'
            }
    
    async def update_user_claimed_levels(self, user_id: int, new_level: int) -> bool:
        """Update user's claimed levels (matching PHP logic exactly)"""
        try:
            from services.user_service import UserService
            user_service = UserService()
            
            user = await user_service.get_user(user_id)
            if not user:
                return False
            
            claimed_levels = user.get('claimed_levels', [])
            if new_level not in claimed_levels:
                claimed_levels.append(new_level)
            
            collection = await get_collection(COLLECTIONS['users'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "claimed_levels": claimed_levels,
                        "updated_at": get_current_timestamp()
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating user claimed levels: {e}")
            return False
    
    async def generate_level_rewards_message(self, user_id: int) -> str:
        """Generate level rewards message (matching PHP logic exactly)"""
        try:
            config = await self.get_level_rewards_config()
            referral_count = await self.get_user_referral_count(user_id)
            current_level = await self.get_user_current_level(user_id)
            claimed_levels = await self.get_user_claimed_levels(user_id)
            eligible_levels = await self.get_user_eligible_levels(user_id)
            next_level_info = await self.get_next_level_info(user_id)
            
            return LevelRewardsModel.format_level_rewards_message(
                user_id, referral_count, current_level, claimed_levels,
                eligible_levels, next_level_info, config
            )
            
        except Exception as e:
            logger.error(f"Error generating level rewards message: {e}")
            return "🏆 <b>Level Rewards System</b>\n\n❌ Error loading level information. Please try again later."
    
    async def generate_level_rewards_keyboard(self, user_id: int):
        """Generate level rewards keyboard (matching PHP logic exactly)"""
        try:
            eligible_levels = await self.get_user_eligible_levels(user_id)
            config = await self.get_level_rewards_config()
            
            return LevelRewardsModel.create_level_rewards_keyboard(eligible_levels, config)
            
        except Exception as e:
            logger.error(f"Error generating level rewards keyboard: {e}")
            # Return basic keyboard if there's an error
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Extra Rewards', callback_data='extraRewards')]
            ])
