"""
Broadcasting data models
Maintains identical structure to PHP JSON format
"""

from typing import Dict, List, Any, Optional, Union
from utils.helpers import get_current_timestamp, get_current_date, generate_broadcast_id

class BroadcastModel:
    """Broadcast data model matching PHP structure exactly"""
    
    @staticmethod
    def create_broadcast_session(
        admin_id: int,
        broadcast_type: str,
        message_data: Dict[str, Any],
        target_users: List[int]
    ) -> Dict[str, Any]:
        """Create broadcast session (matching PHP structure)"""
        
        broadcast_id = generate_broadcast_id()
        
        return {
            "broadcast_id": broadcast_id,
            "admin_id": admin_id,
            "type": broadcast_type,
            "message_data": message_data,
            "target_users": target_users,
            "total_users": len(target_users),
            "sent_count": 0,
            "failed_count": 0,
            "blocked_count": 0,
            "status": "active",
            "cancelled": False,
            "created_at": get_current_timestamp(),
            "updated_at": get_current_timestamp(),
            "start_time": get_current_timestamp(),
            "processed_users": [],
            "errors": []
        }
    
    @staticmethod
    def create_text_message_data(text: str) -> Dict[str, Any]:
        """Create text message data structure"""
        return {
            "type": "text",
            "text": text
        }
    
    @staticmethod
    def create_photo_message_data(photo_file_id: str, caption: str = "") -> Dict[str, Any]:
        """Create photo message data structure"""
        return {
            "type": "photo",
            "photo": photo_file_id,
            "caption": caption
        }
    
    @staticmethod
    def create_video_message_data(video_file_id: str, caption: str = "") -> Dict[str, Any]:
        """Create video message data structure"""
        return {
            "type": "video",
            "video": video_file_id,
            "caption": caption
        }
    
    @staticmethod
    def create_document_message_data(document_file_id: str, caption: str = "") -> Dict[str, Any]:
        """Create document message data structure"""
        return {
            "type": "document",
            "document": document_file_id,
            "caption": caption
        }
    
    @staticmethod
    def create_audio_message_data(audio_file_id: str, caption: str = "") -> Dict[str, Any]:
        """Create audio message data structure"""
        return {
            "type": "audio",
            "audio": audio_file_id,
            "caption": caption
        }
    
    @staticmethod
    def format_broadcast_progress_message(
        broadcast_id: str,
        processed: int,
        total: int,
        success: int,
        failed: int,
        blocked: int,
        percent: float,
        eta_seconds: int
    ) -> str:
        """Format broadcast progress message (matching PHP exactly)"""
        
        # Format ETA
        if eta_seconds < 60:
            eta = f"{eta_seconds}s"
        elif eta_seconds < 3600:
            eta = f"{eta_seconds // 60}m {eta_seconds % 60}s"
        else:
            hours = eta_seconds // 3600
            minutes = (eta_seconds % 3600) // 60
            eta = f"{hours}h {minutes}m"
        
        # Create progress bar
        progress_bar = BroadcastModel.create_progress_bar(percent)
        
        message = "📊 <b>Broadcast Progress</b>\n\n"
        message += f"🆔 <b>ID:</b> <code>{broadcast_id[-8:]}</code>\n"
        message += f"📈 <b>Progress:</b> {processed}/{total} ({percent:.1f}%)\n"
        message += f"✅ <b>Success:</b> {success}\n"
        message += f"❌ <b>Failed:</b> {failed}\n"
        message += f"🚫 <b>Blocked:</b> {blocked}\n"
        message += f"⏱️ <b>ETA:</b> {eta}\n\n"
        message += f"{progress_bar}\n\n"
        message += "💡 Send /cancel to stop the broadcast"
        
        return message
    
    @staticmethod
    def create_progress_bar(percent: float, length: int = 20) -> str:
        """Create progress bar (matching PHP version)"""
        filled = int((percent / 100) * length)
        empty = length - filled
        return "█" * filled + "░" * empty
    
    @staticmethod
    def format_broadcast_completion_message(
        broadcast_id: str,
        total_users: int,
        success_count: int,
        failed_count: int,
        blocked_count: int,
        duration_seconds: int,
        success_rate: float,
        broadcast_type: str = "message"
    ) -> str:
        """Format broadcast completion message (matching PHP exactly)"""
        
        # Format duration
        if duration_seconds < 60:
            duration = f"{duration_seconds}s"
        elif duration_seconds < 3600:
            duration = f"{duration_seconds // 60}m {duration_seconds % 60}s"
        else:
            hours = duration_seconds // 3600
            minutes = (duration_seconds % 3600) // 60
            duration = f"{hours}h {minutes}m"
        
        broadcast_type_display = "Gift Broadcast" if broadcast_type == "gift" else "Message Broadcast"
        
        message = f"✅ <b>{broadcast_type_display} Completed!</b>\n\n"
        message += f"🆔 <b>Broadcast ID:</b> <code>{broadcast_id[-8:]}</code>\n"
        message += f"👥 <b>Total Users:</b> {total_users}\n"
        message += f"✅ <b>Successfully Sent:</b> {success_count}\n"
        message += f"❌ <b>Failed:</b> {failed_count}\n"
        message += f"🚫 <b>Blocked Users:</b> {blocked_count}\n"
        message += f"📊 <b>Success Rate:</b> {success_rate:.1f}%\n"
        message += f"⏱️ <b>Duration:</b> {duration}\n\n"
        message += f"🎉 Broadcast completed successfully!"
        
        return message
    
    @staticmethod
    def detect_message_type(message_data: Dict[str, Any]) -> str:
        """Detect message type from message data"""
        return message_data.get("type", "text")

class GiftBroadcastModel:
    """Gift broadcast model matching PHP structure exactly"""
    
    @staticmethod
    def create_gift_broadcast_data(
        channel: str,
        amount: float,
        channel_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create gift broadcast data (matching PHP structure)"""
        
        broadcast_id = f"gift_{get_current_timestamp()}"
        
        return {
            "channel": channel,
            "amount": amount,
            "created_at": get_current_timestamp(),
            "broadcast_id": broadcast_id,
            "channel_type": channel_data.get("type", "public"),
            "channel_id": channel_data.get("id"),
            "invite_link": channel_data.get("invite_link"),
            "channel_title": channel_data.get("title", channel),
            "username": channel_data.get("username", "")
        }
    
    @staticmethod
    def format_gift_message(
        channel_title: str,
        amount: float,
        join_link: str,
        join_text: str = "Click & Join Channel"
    ) -> str:
        """Format gift message (matching PHP exactly)"""
        
        message = f"🎁 <b>Free Money Alert!</b>\n\n"
        message += f"💰 Get ₹{amount} instantly by joining our channel!\n\n"
        message += f"📢 <b>Channel:</b> {channel_title}\n\n"
        message += f"🔥 <b>How to claim:</b>\n"
        message += f"1️⃣ Click the button below\n"
        message += f"2️⃣ Join the channel\n"
        message += f"3️⃣ Come back and click 'Claim Gift'\n\n"
        message += f"⚡ Limited time offer - Claim now!"
        
        return message
    
    @staticmethod
    def create_gift_keyboard(join_link: str, join_text: str = "Click & Join Channel"):
        """Create gift keyboard (matching PHP structure)"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        return InlineKeyboardMarkup([
            [InlineKeyboardButton(join_text, url=join_link)],
            [InlineKeyboardButton('🎁 Claim Gift', callback_data='claimGift')]
        ])

class GiftCodeModel:
    """Gift code model matching PHP structure exactly"""
    
    @staticmethod
    def create_gift_code(
        code: str,
        amount: float,
        usage_limit: int,
        created_by: int,
        expiry_date: int = 0
    ) -> Dict[str, Any]:
        """Create gift code data structure"""
        
        return {
            "code": code,
            "amount": amount,
            "usage_limit": usage_limit,
            "expiry_date": expiry_date,
            "created_by": created_by,
            "created_at": get_current_timestamp(),
            "used_count": 0,
            "last_used": 0,
            "redeemed_by": []
        }
    
    @staticmethod
    def validate_gift_code_format(code: str) -> Dict[str, Any]:
        """Validate gift code format"""
        errors = []
        
        if len(code) < 3:
            errors.append("Code must be at least 3 characters long")
        
        if len(code) > 20:
            errors.append("Code must be no more than 20 characters long")
        
        if not code.isalnum():
            errors.append("Code must contain only letters and numbers")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def can_redeem_code(gift_code: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """Check if user can redeem gift code"""
        
        # Check if code has expired
        if gift_code.get("expiry_date", 0) > 0 and get_current_timestamp() > gift_code["expiry_date"]:
            return {"can_redeem": False, "reason": "Gift code has expired"}
        
        # Check if user has already redeemed this code
        redeemed_by = gift_code.get("redeemed_by", [])
        if user_id in redeemed_by:
            return {"can_redeem": False, "reason": "You have already redeemed this gift code"}
        
        # Check usage limit
        usage_limit = gift_code.get("usage_limit", 0)
        used_count = gift_code.get("used_count", 0)
        
        if usage_limit > 0 and used_count >= usage_limit:
            return {"can_redeem": False, "reason": "Gift code usage limit reached"}
        
        return {"can_redeem": True, "reason": ""}
    
    @staticmethod
    def format_gift_code_info(gift_code: Dict[str, Any]) -> str:
        """Format gift code information for display"""
        
        code = gift_code["code"]
        amount = gift_code["amount"]
        usage_limit = gift_code.get("usage_limit", 0)
        used_count = gift_code.get("used_count", 0)
        created_at = gift_code.get("created_at", 0)
        
        # Format creation date
        from datetime import datetime
        created_date = datetime.fromtimestamp(created_at).strftime("%Y-%m-%d %H:%M")
        
        message = f"🎫 <b>Gift Code Details</b>\n\n"
        message += f"📝 <b>Code:</b> <code>{code}</code>\n"
        message += f"💰 <b>Amount:</b> ₹{amount}\n"
        message += f"🔢 <b>Usage:</b> {used_count}"
        
        if usage_limit > 0:
            message += f"/{usage_limit}"
        else:
            message += " (Unlimited)"
        
        message += f"\n📅 <b>Created:</b> {created_date}"
        
        return message
