"""
User data model
Maintains identical structure to PHP JSON format
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from utils.helpers import get_current_timestamp, get_referral_link
from config.settings import settings

class UserModel:
    """User data model matching PHP JSON structure exactly"""
    
    @staticmethod
    def create_new_user(
        user_id: int, 
        first_name: str, 
        last_name: str, 
        username: str, 
        referred_by: str = 'None'
    ) -> Dict[str, Any]:
        """Create new user document (matching PHP structure)"""
        
        # Generate referral link
        referral_link = get_referral_link(user_id, settings.BOT_USERNAME)
        
        return {
            "user_id": user_id,
            "first_name": first_name,
            "last_name": last_name,
            "username": username,
            "banned": False,
            "referred": False,
            "referred_by": referred_by,
            "joining_bonus_got": 0,
            "referral_link": referral_link,
            "balance": 0,
            "successful_withdraw": 0,
            "withdraw_under_review": 0,
            "gift_claimed": False,
            "claimed_levels": [],
            "account_info": {
                "name": "",
                "ifsc": "",
                "email": "",
                "account_number": "",
                "mobile_number": "",
                "withdrawal_method": "",
                "usdt_address": "",
                "binance_id": ""
            },
            "promotion_report": [],
            "withdrawal_reports": [],
            "created_at": get_current_timestamp(),
            "updated_at": get_current_timestamp()
        }
    
    @staticmethod
    def validate_user_data(user_data: Dict[str, Any]) -> bool:
        """Validate user data structure"""
        required_fields = [
            'user_id', 'first_name', 'username', 'banned', 'referred',
            'referred_by', 'joining_bonus_got', 'referral_link', 'balance',
            'successful_withdraw', 'withdraw_under_review', 'gift_claimed',
            'claimed_levels', 'account_info', 'promotion_report'
        ]
        
        for field in required_fields:
            if field not in user_data:
                return False
        
        # Validate account_info structure
        account_info = user_data.get('account_info', {})
        account_fields = [
            'name', 'ifsc', 'email', 'account_number', 
            'mobile_number', 'withdrawal_method', 'usdt_address'
        ]
        
        for field in account_fields:
            if field not in account_info:
                return False
        
        return True
    
    @staticmethod
    def update_user_balance(user_data: Dict[str, Any], amount: int, operation: str = 'add') -> Dict[str, Any]:
        """Update user balance"""
        current_balance = user_data.get('balance', 0)
        
        if operation == 'add':
            user_data['balance'] = current_balance + amount
        elif operation == 'subtract':
            user_data['balance'] = max(0, current_balance - amount)
        elif operation == 'set':
            user_data['balance'] = amount
        
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def add_promotion_report(
        user_data: Dict[str, Any], 
        referred_user_name: str, 
        referred_user_id: int, 
        amount_got: int
    ) -> Dict[str, Any]:
        """Add promotion report entry"""
        if 'promotion_report' not in user_data:
            user_data['promotion_report'] = []
        
        report_entry = {
            "referred_user_name": referred_user_name,
            "referred_user_id": referred_user_id,
            "amount_got": amount_got
        }
        
        user_data['promotion_report'].append(report_entry)
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def add_withdrawal_report(
        user_data: Dict[str, Any],
        amount: int,
        status: str,
        date: str,
        method: str = ""
    ) -> Dict[str, Any]:
        """Add withdrawal report entry"""
        if 'withdrawal_reports' not in user_data:
            user_data['withdrawal_reports'] = []
        
        report_entry = {
            "amount": amount,
            "status": status,
            "date": date,
            "method": method,
            "timestamp": get_current_timestamp()
        }
        
        user_data['withdrawal_reports'].append(report_entry)
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def update_account_info(user_data: Dict[str, Any], field: str, value: str) -> Dict[str, Any]:
        """Update account information field"""
        if 'account_info' not in user_data:
            user_data['account_info'] = {}
        
        user_data['account_info'][field] = value
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def claim_level_bonus(user_data: Dict[str, Any], level: int) -> Dict[str, Any]:
        """Mark level bonus as claimed"""
        if 'claimed_levels' not in user_data:
            user_data['claimed_levels'] = []
        
        if level not in user_data['claimed_levels']:
            user_data['claimed_levels'].append(level)
        
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def set_withdrawal_under_review(user_data: Dict[str, Any], amount: int) -> Dict[str, Any]:
        """Set withdrawal amount under review"""
        user_data['withdraw_under_review'] = amount
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def complete_withdrawal(user_data: Dict[str, Any], status: str) -> Dict[str, Any]:
        """Complete withdrawal process"""
        amount = user_data.get('withdraw_under_review', 0)
        
        if status == 'Passed':
            # Add to successful withdrawals
            user_data['successful_withdraw'] = user_data.get('successful_withdraw', 0) + amount
        elif status == 'Failed':
            # Return money to balance
            user_data['balance'] = user_data.get('balance', 0) + amount
        
        # Clear withdrawal under review
        user_data['withdraw_under_review'] = 0
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def ban_user(user_data: Dict[str, Any], banned: bool = True) -> Dict[str, Any]:
        """Ban or unban user"""
        user_data['banned'] = banned
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def mark_referred(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Mark user as having been processed for referral"""
        user_data['referred'] = True
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def set_gift_claimed(user_data: Dict[str, Any], claimed: bool = True) -> Dict[str, Any]:
        """Set gift claimed status"""
        user_data['gift_claimed'] = claimed
        user_data['updated_at'] = get_current_timestamp()
        return user_data
    
    @staticmethod
    def get_total_referrals(user_data: Dict[str, Any]) -> int:
        """Get total number of referrals"""
        return len(user_data.get('promotion_report', []))
    
    @staticmethod
    def get_total_referral_earnings(user_data: Dict[str, Any]) -> int:
        """Get total earnings from referrals"""
        promotion_report = user_data.get('promotion_report', [])
        return sum(report.get('amount_got', 0) for report in promotion_report)
    
    @staticmethod
    def is_account_info_complete(user_data: Dict[str, Any]) -> bool:
        """Check if account information is complete"""
        account_info = user_data.get('account_info', {})
        
        required_fields = ['name', 'email', 'mobile_number']
        
        # Check withdrawal method specific requirements
        withdrawal_method = account_info.get('withdrawal_method', '')
        
        if withdrawal_method == 'bank':
            required_fields.extend(['account_number', 'ifsc'])
        elif withdrawal_method == 'usdt':
            required_fields.append('usdt_address')
        
        for field in required_fields:
            if not account_info.get(field, '').strip():
                return False
        
        return True
    
    @staticmethod
    def get_missing_account_fields(user_data: Dict[str, Any]) -> List[str]:
        """Get list of missing account information fields"""
        account_info = user_data.get('account_info', {})
        missing_fields = []
        
        required_fields = {
            'name': 'Full Name',
            'email': 'Email Address',
            'mobile_number': 'Mobile Number'
        }
        
        # Check withdrawal method specific requirements
        withdrawal_method = account_info.get('withdrawal_method', '')
        
        if withdrawal_method == 'bank':
            required_fields.update({
                'account_number': 'Account Number',
                'ifsc': 'IFSC Code'
            })
        elif withdrawal_method == 'usdt':
            required_fields['usdt_address'] = 'USDT Address'
        
        for field, display_name in required_fields.items():
            if not account_info.get(field, '').strip():
                missing_fields.append(display_name)
        
        return missing_fields
